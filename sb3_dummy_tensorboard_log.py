import os
import time
import random
import math
from torch.utils.tensorboard import SummaryWriter
# Using Rich for formatted table printing
# Install with: pip install rich
# from rich.console import Console # Reverted: Not using rich
# from rich.table import Table # Reverted: Not using rich

# --- Parameters ---
TB_LOG_DIR = "./sb3_dummy_tb_only/"
TB_EXPERIMENT_NAME = "SAC_InspireHandForceEnv"
NUM_DUMMY_STEPS = 1000
PRINT_INTERVAL_STEPS = 100 # How often to print the table

# Dummy value ranges
INITIAL_REWARD = -100
FINAL_REWARD = 100
INITIAL_LOSS = 1.0
FINAL_LOSS = 0.05
RANDOM_REWARD_FLUCTUATION = 60
MEAN_EP_LENGTH = 200
LEARNING_RATE = 0.0003

# --- Setup Logger / Writer ---
log_path = os.path.join(TB_LOG_DIR, TB_EXPERIMENT_NAME + "_1")
os.makedirs(log_path, exist_ok=True)
writer = SummaryWriter(log_dir=log_path)

# --- Create Rich Console --- (Reverted)
# console = Console()

# --- Helper Function for Printing (Manual Formatting) ---
def print_formatted_log(data: dict):
    """Prints the collected data in a formatted table similar to SB3 (manual)."""
    # Define categories and their order
    categories = {
        "eval": ["mean_ep_length", "mean_reward"],
        "rollout": ["ep_len_mean", "ep_rew_mean"],
        "time": ["fps", "iterations", "time_elapsed", "total_timesteps"],
        "train": [
            "approx_kl", "clip_fraction", "clip_range", "entropy_loss",
            "explained_variance", "learning_rate", "loss", "n_updates",
            "policy_gradient_loss", "std", "value_loss"
        ]
    }
    # Max width for keys and values for alignment
    key_width = max(len(k) for cat in categories.values() for k in cat) + 4 # Add padding for indent
    val_width = 18 # Increased width for right-aligned values
    total_width = key_width + val_width + 7 # | key | value |

    print("-" * total_width)
    for cat_name, keys in categories.items():
        print(f"| {cat_name + '/':<{key_width}} | {'':>{val_width}} |") # Category header
        for key in keys:
            metric_name = "    " + key # Indent metric name (already included in key_width padding)
            if key in data:
                value = data[key]
                if isinstance(value, float):
                    val_str = f"{value:.5g}" # Format float
                else:
                    val_str = str(value) # Format int/other
                # Truncate if too long
                val_str = val_str[:val_width]
                # Print row with alignment
                print(f"| {metric_name:<{key_width}} | {val_str:>{val_width}} |")
            else:
                 print(f"| {metric_name:<{key_width}} | {'N/A':>{val_width}} |") # Handle missing keys

    print("-" * total_width)

# --- Simulate Data Logging ---
print(f"--- Starting dummy data logging for {NUM_DUMMY_STEPS} steps ---")
print(f"TensorBoard logs will be saved in: {os.path.abspath(log_path)}")

start_time = time.time()
dummy_loss = INITIAL_LOSS
loss_step = (FINAL_LOSS - INITIAL_LOSS) / NUM_DUMMY_STEPS
reward_center = (INITIAL_REWARD + FINAL_REWARD) / 2

# Dictionary to hold current log values
current_log_data = {}

for i in range(NUM_DUMMY_STEPS):
    # --- Simulate Metric Updates ---
    # Eval metrics (usually updated less frequently, but we simulate each step)
    current_log_data["eval/mean_ep_length"] = MEAN_EP_LENGTH + random.randint(-10, 10)
    current_log_data["eval/mean_reward"] = reward_center + random.uniform(-RANDOM_REWARD_FLUCTUATION, RANDOM_REWARD_FLUCTUATION)

    # Rollout metrics
    current_log_data["rollout/ep_len_mean"] = MEAN_EP_LENGTH + random.randint(-5, 5)
    current_log_data["rollout/ep_rew_mean"] = reward_center + random.uniform(-RANDOM_REWARD_FLUCTUATION, RANDOM_REWARD_FLUCTUATION)

    # Time metrics
    current_log_data["time/fps"] = random.randint(500, 1500)
    current_log_data["time/iterations"] = (i // PRINT_INTERVAL_STEPS) + 1 # Approximate
    current_log_data["time/time_elapsed"] = int(time.time() - start_time)
    current_log_data["time/total_timesteps"] = i + 1

    # Train metrics
    dummy_loss += loss_step + random.uniform(-0.01, 0.01)
    dummy_loss = max(dummy_loss, FINAL_LOSS)
    current_log_data["train/approx_kl"] = max(0.0, random.gauss(0.02, 0.01))
    current_log_data["train/clip_fraction"] = random.uniform(0.05, 0.25)
    current_log_data["train/clip_range"] = 0.2
    current_log_data["train/entropy_loss"] = random.gauss(-1.5, 0.2)
    current_log_data["train/explained_variance"] = max(0.0, min(1.0, random.gauss(0.98, 0.02)))
    current_log_data["train/learning_rate"] = LEARNING_RATE # Could simulate decay
    current_log_data["train/loss"] = dummy_loss
    current_log_data["train/n_updates"] = i # Each step could be an update here
    current_log_data["train/policy_gradient_loss"] = random.gauss(-0.02, 0.01)
    current_log_data["train/std"] = max(0.1, random.gauss(0.4, 0.1))
    current_log_data["train/value_loss"] = max(0.0, dummy_loss + random.gauss(0.0, 0.05))

    # --- Write ALL metrics to TensorBoard ---
    for key, value in current_log_data.items():
        # TensorBoard uses '/' separators directly
        writer.add_scalar(key, value, global_step=i)

    # --- Print Formatted Table Periodically ---
    if (i + 1) % PRINT_INTERVAL_STEPS == 0:
        print(f"\n--- Step {i + 1}/{NUM_DUMMY_STEPS} ---")
        # Create a dictionary formatted for the print function (without category prefixes)
        print_data = {k.split('/')[1]: v for k, v in current_log_data.items()}
        print_formatted_log(print_data)
        # print(f"Logged dummy step {i + 1}/{NUM_DUMMY_STEPS}") # Replaced by table

print(f"\n--- Dummy data logging finished --- ({int(time.time() - start_time)} seconds) ---")

# --- How to view TensorBoard ---
print("\\n--- To view TensorBoard logs, run the following command in your terminal: ---")
# Point logdir to the parent directory containing the experiment folder
print(f"tensorboard --logdir {os.path.abspath(TB_LOG_DIR)}")
print("Then open the URL provided by TensorBoard in your web browser.") 

# Close the SummaryWriter
writer.close() 