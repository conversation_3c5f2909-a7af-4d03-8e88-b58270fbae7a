"""
Network Configuration System

This module provides configuration classes and preset configurations
for different neural network architectures.
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Union
from enum import Enum
import yaml
import json


class NetworkType(Enum):
    """Supported network architecture types"""
    MLP = "mlp"
    RESIDUAL = "residual"
    ATTENTION = "attention"
    MULTI_MODAL = "multi_modal"
    HYBRID = "hybrid"


@dataclass
class NetworkConfig:
    """
    Configuration class for neural network architectures
    
    This class defines all configurable parameters for custom networks
    """
    
    # Basic architecture
    network_type: NetworkType = NetworkType.MLP
    hidden_dims: List[int] = field(default_factory=lambda: [256, 256])
    
    # Activation and normalization
    activation: str = "relu"
    normalization: str = "layer_norm"
    dropout_rate: float = 0.0
    
    # Residual network specific
    use_residual: bool = False
    residual_blocks: int = 2
    
    # Attention specific
    use_attention: bool = False
    num_attention_heads: int = 8
    attention_dropout: float = 0.1
    
    # Multi-modal specific
    input_encoders: Optional[List[Dict[str, Any]]] = None
    fusion_method: str = "concat"
    
    # Advanced features
    use_layer_norm: bool = True
    use_skip_connections: bool = False
    weight_init: str = "xavier"
    
    # Actor/Critic specific configurations
    shared_features: bool = False
    actor_config: Optional[Dict[str, Any]] = None
    critic_config: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Validate configuration after initialization"""
        if isinstance(self.network_type, str):
            self.network_type = NetworkType(self.network_type.lower())
        
        # Set default actor/critic configs if not provided
        if self.actor_config is None:
            self.actor_config = {
                "hidden_dims": self.hidden_dims.copy(),
                "activation": self.activation,
                "normalization": self.normalization,
                "dropout_rate": self.dropout_rate,
            }
        
        if self.critic_config is None:
            self.critic_config = {
                "hidden_dims": self.hidden_dims.copy(),
                "activation": self.activation,
                "normalization": self.normalization,
                "dropout_rate": self.dropout_rate,
            }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        config_dict = {}
        for key, value in self.__dict__.items():
            if isinstance(value, Enum):
                config_dict[key] = value.value
            else:
                config_dict[key] = value
        return config_dict
    
    def save(self, filepath: str):
        """Save configuration to file (YAML or JSON)"""
        config_dict = self.to_dict()
        
        if filepath.endswith('.yaml') or filepath.endswith('.yml'):
            with open(filepath, 'w') as f:
                yaml.dump(config_dict, f, default_flow_style=False)
        elif filepath.endswith('.json'):
            with open(filepath, 'w') as f:
                json.dump(config_dict, f, indent=2)
        else:
            raise ValueError("Unsupported file format. Use .yaml, .yml, or .json")
    
    @classmethod
    def load(cls, filepath: str) -> 'NetworkConfig':
        """Load configuration from file"""
        if filepath.endswith('.yaml') or filepath.endswith('.yml'):
            with open(filepath, 'r') as f:
                config_dict = yaml.safe_load(f)
        elif filepath.endswith('.json'):
            with open(filepath, 'r') as f:
                config_dict = json.load(f)
        else:
            raise ValueError("Unsupported file format. Use .yaml, .yml, or .json")
        
        return cls(**config_dict)


def get_preset_config(preset_name: str) -> NetworkConfig:
    """
    Get predefined network configurations
    
    Args:
        preset_name: Name of the preset configuration
        
    Returns:
        NetworkConfig instance with preset parameters
    """
    
    presets = {
        "basic_mlp": NetworkConfig(
            network_type=NetworkType.MLP,
            hidden_dims=[256, 256],
            activation="relu",
            normalization="layer_norm",
            dropout_rate=0.0,
        ),
        
        "deep_mlp": NetworkConfig(
            network_type=NetworkType.MLP,
            hidden_dims=[512, 512, 256, 256],
            activation="relu",
            normalization="layer_norm",
            dropout_rate=0.1,
        ),
        
        "wide_mlp": NetworkConfig(
            network_type=NetworkType.MLP,
            hidden_dims=[1024, 512],
            activation="swish",
            normalization="layer_norm",
            dropout_rate=0.05,
        ),
        
        "residual_net": NetworkConfig(
            network_type=NetworkType.RESIDUAL,
            hidden_dims=[256, 256],
            activation="relu",
            normalization="layer_norm",
            dropout_rate=0.1,
            use_residual=True,
            residual_blocks=3,
        ),
        
        "attention_net": NetworkConfig(
            network_type=NetworkType.ATTENTION,
            hidden_dims=[256, 256],
            activation="gelu",
            normalization="layer_norm",
            dropout_rate=0.1,
            use_attention=True,
            num_attention_heads=8,
            attention_dropout=0.1,
        ),
        
        "lightweight": NetworkConfig(
            network_type=NetworkType.MLP,
            hidden_dims=[128, 64],
            activation="relu",
            normalization="none",
            dropout_rate=0.0,
        ),
        
        "triple_256_mlp": NetworkConfig(
            network_type=NetworkType.MLP,
            hidden_dims=[256, 256, 256],
            activation="relu",
            normalization="layer_norm",
            dropout_rate=0.0,
        ),
        
        "robust": NetworkConfig(
            network_type=NetworkType.RESIDUAL,
            hidden_dims=[512, 512, 256],
            activation="swish",
            normalization="layer_norm",
            dropout_rate=0.2,
            use_residual=True,
            residual_blocks=4,
        ),
        
        "multi_modal": NetworkConfig(
            network_type=NetworkType.MULTI_MODAL,
            hidden_dims=[256, 128],
            activation="relu",
            normalization="layer_norm",
            dropout_rate=0.1,
            input_encoders=[
                {"type": "joint_states", "dim": 24, "hidden_dims": [128]},
                {"type": "forces", "dim": 15, "hidden_dims": [64]},
                {"type": "object_pose", "dim": 9, "hidden_dims": [32]},
            ],
            fusion_method="concat",
        ),
        
        "hybrid_attention": NetworkConfig(
            network_type=NetworkType.HYBRID,
            hidden_dims=[512, 256, 256],
            activation="gelu",
            normalization="layer_norm",
            dropout_rate=0.1,
            use_residual=True,
            use_attention=True,
            num_attention_heads=8,
            attention_dropout=0.1,
            residual_blocks=2,
        ),
    }
    
    if preset_name not in presets:
        available_presets = list(presets.keys())
        raise ValueError(f"Unknown preset '{preset_name}'. Available presets: {available_presets}")
    
    return presets[preset_name]


def create_custom_config(
    network_type: str = "mlp",
    hidden_dims: List[int] = None,
    activation: str = "relu",
    **kwargs
) -> NetworkConfig:
    """
    Create a custom network configuration
    
    Args:
        network_type: Type of network architecture
        hidden_dims: List of hidden layer dimensions
        activation: Activation function name
        **kwargs: Additional configuration parameters
        
    Returns:
        NetworkConfig instance with custom parameters
    """
    if hidden_dims is None:
        hidden_dims = [256, 256]
    
    return NetworkConfig(
        network_type=NetworkType(network_type.lower()),
        hidden_dims=hidden_dims,
        activation=activation,
        **kwargs
    )


# Predefined configurations for different use cases
FORCE_CONTROL_CONFIG = NetworkConfig(
    network_type=NetworkType.HYBRID,
    hidden_dims=[512, 256, 128],
    activation="swish",
    normalization="layer_norm",
    dropout_rate=0.1,
    use_residual=True,
    use_attention=True,
    num_attention_heads=4,
    residual_blocks=2,
    actor_config={
        "hidden_dims": [512, 256],
        "activation": "tanh",
        "dropout_rate": 0.05,
    },
    critic_config={
        "hidden_dims": [512, 512, 256],
        "activation": "swish", 
        "dropout_rate": 0.1,
    }
)

DEXTEROUS_MANIPULATION_CONFIG = NetworkConfig(
    network_type=NetworkType.ATTENTION,
    hidden_dims=[256, 256, 128],
    activation="gelu",
    normalization="layer_norm",
    dropout_rate=0.15,
    use_attention=True,
    num_attention_heads=8,
    attention_dropout=0.1,
)

FAST_TRAINING_CONFIG = NetworkConfig(
    network_type=NetworkType.MLP,
    hidden_dims=[128, 128],
    activation="relu",
    normalization="none",
    dropout_rate=0.0,
) 