"""
Custom SAC Policy Classes

This module provides custom policy classes for Stable-Baselines3 SAC
that support configurable network architectures.
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Any, Dict, List, Optional, Tuple, Type, Union
from gymnasium import spaces

from stable_baselines3.common.policies import BasePolicy
from stable_baselines3.common.torch_layers import BaseFeaturesExtractor
from stable_baselines3.common.type_aliases import Schedule
from stable_baselines3.sac.policies import SACPolicy

from .network_factory import NetworkFactory
from .network_configs import NetworkConfig, get_preset_config


class CustomFeaturesExtractor(BaseFeaturesExtractor):
    """
    Custom features extractor using configurable networks
    """
    
    def __init__(
        self,
        observation_space: spaces.Space,
        features_dim: int = 256,
        network_config: Optional[NetworkConfig] = None,
    ):
        super().__init__(observation_space, features_dim)
        
        if network_config is None:
            network_config = get_preset_config("basic_mlp")
        
        # Get input dimension from observation space
        if isinstance(observation_space, spaces.Box):
            input_dim = observation_space.shape[0]
        else:
            raise ValueError(f"Unsupported observation space: {observation_space}")
        
        # Create feature extractor network
        self.extractor = NetworkFactory.create_network(
            config=network_config,
            input_dim=input_dim,
            output_dim=features_dim,
            network_role="shared"
        )
        
        # Initialize weights
        NetworkFactory.initialize_weights(self.extractor, network_config.weight_init)
    
    def forward(self, observations: torch.Tensor) -> torch.Tensor:
        return self.extractor(observations)


class CustomSACPolicy(SACPolicy):
    """
    Custom SAC Policy with configurable network architectures
    
    This policy allows users to specify different network configurations
    for the actor and critic networks.
    """
    
    def __init__(
        self,
        observation_space: spaces.Space,
        action_space: spaces.Space,
        lr_schedule: Schedule,
        net_arch: Optional[Union[List[int], Dict[str, List[int]]]] = None,
        activation_fn: Type[nn.Module] = nn.ReLU,
        use_sde: bool = False,
        log_std_init: float = -3,
        use_expln: bool = False,
        clip_mean: float = 2.0,
        features_extractor_class: Type[BaseFeaturesExtractor] = CustomFeaturesExtractor,
        features_extractor_kwargs: Optional[Dict[str, Any]] = None,
        normalize_images: bool = True,
        optimizer_class: Type[torch.optim.Optimizer] = torch.optim.Adam,
        optimizer_kwargs: Optional[Dict[str, Any]] = None,
        n_critics: int = 2,
        share_features_extractor: bool = False,
        # Custom parameters
        network_config: Optional[Union[NetworkConfig, str]] = None,
        actor_config: Optional[Union[NetworkConfig, str]] = None,
        critic_config: Optional[Union[NetworkConfig, str]] = None,
        features_dim: int = 256,
        **kwargs,
    ):
        """
        Initialize Custom SAC Policy
        
        Args:
            network_config: Main network configuration (used if actor/critic configs not specified)
            actor_config: Specific configuration for actor network
            critic_config: Specific configuration for critic network
            features_dim: Dimension of the features extractor output
            **kwargs: Additional arguments passed to parent class
        """
        
        # Handle network configurations
        if isinstance(network_config, str):
            network_config = get_preset_config(network_config)
        elif network_config is None:
            network_config = get_preset_config("basic_mlp")
        
        if isinstance(actor_config, str):
            actor_config = get_preset_config(actor_config)
        elif actor_config is None:
            actor_config = network_config
        
        if isinstance(critic_config, str):
            critic_config = get_preset_config(critic_config)
        elif critic_config is None:
            critic_config = network_config
        
        # Store configurations
        self.network_config = network_config
        self.actor_config = actor_config
        self.critic_config = critic_config
        self.features_dim = features_dim
        
        # Set up features extractor kwargs
        if features_extractor_kwargs is None:
            features_extractor_kwargs = {}
        
        features_extractor_kwargs.update({
            "features_dim": features_dim,
            "network_config": network_config,
        })
        
        # Convert custom network config to net_arch format for compatibility
        if net_arch is None:
            net_arch = {
                "pi": actor_config.hidden_dims,
                "qf": critic_config.hidden_dims,
            }
        
        # Initialize parent class
        super().__init__(
            observation_space=observation_space,
            action_space=action_space,
            lr_schedule=lr_schedule,
            net_arch=net_arch,
            activation_fn=activation_fn,
            use_sde=use_sde,
            log_std_init=log_std_init,
            use_expln=use_expln,
            clip_mean=clip_mean,
            features_extractor_class=features_extractor_class,
            features_extractor_kwargs=features_extractor_kwargs,
            normalize_images=normalize_images,
            optimizer_class=optimizer_class,
            optimizer_kwargs=optimizer_kwargs,
            n_critics=n_critics,
            share_features_extractor=share_features_extractor,
            **kwargs,
        )
    
    def make_actor(self, features_extractor: Optional[BaseFeaturesExtractor] = None) -> "CustomActor":
        """Create the actor network"""
        actor_kwargs = self._update_features_extractor(
            self.actor_kwargs, features_extractor
        )
        
        return CustomActor(
            network_config=self.actor_config,
            **actor_kwargs
        ).to(self.device)
    
    def make_critic(self, features_extractor: Optional[BaseFeaturesExtractor] = None) -> "CustomCritic":
        """Create the critic network"""
        critic_kwargs = self._update_features_extractor(
            self.critic_kwargs, features_extractor
        )
        
        return CustomCritic(
            network_config=self.critic_config,
            n_critics=self.n_critics,
            **critic_kwargs
        ).to(self.device)
    
    def get_network_info(self) -> Dict[str, Any]:
        """Get information about the network architectures"""
        info = {
            "actor_info": NetworkFactory.get_model_info(self.actor),
            "critic_info": NetworkFactory.get_model_info(self.critic),
        }
        
        if hasattr(self, 'features_extractor'):
            info["features_extractor_info"] = NetworkFactory.get_model_info(self.features_extractor)
        
        return info


class CustomActor(nn.Module):
    """
    Custom Actor network for SAC
    """
    
    def __init__(
        self,
        observation_space: spaces.Space,
        action_space: spaces.Space,
        features_extractor: BaseFeaturesExtractor,
        features_dim: int,
        net_arch: List[int],
        activation_fn: Type[nn.Module] = nn.ReLU,
        use_sde: bool = False,
        log_std_init: float = -3,
        use_expln: bool = False,
        clip_mean: float = 2.0,
        normalize_images: bool = True,
        network_config: Optional[NetworkConfig] = None,
        **kwargs,
    ):
        super().__init__()
        
        self.observation_space = observation_space
        self.action_space = action_space
        self.features_extractor = features_extractor
        self.features_dim = features_dim
        self.use_sde = use_sde
        self.use_expln = use_expln
        self.clip_mean = clip_mean
        self.normalize_images = normalize_images
        
        if network_config is None:
            network_config = get_preset_config("basic_mlp")
        
        self.network_config = network_config
        
        # Get action dimension
        if isinstance(action_space, spaces.Box):
            action_dim = action_space.shape[0]
        else:
            raise ValueError(f"Unsupported action space: {action_space}")
        
        # Create mean network
        self.mu = NetworkFactory.create_network(
            config=network_config,
            input_dim=features_dim,
            output_dim=action_dim,
            network_role="actor"
        )
        
        # Create log std network
        if use_sde:
            self.log_std = None  # Will be handled by SDE
        else:
            self.log_std = NetworkFactory.create_network(
                config=network_config,
                input_dim=features_dim,
                output_dim=action_dim,
                network_role="actor"
            )
        
        # Initialize weights
        NetworkFactory.initialize_weights(self.mu, network_config.weight_init)
        if self.log_std is not None:
            NetworkFactory.initialize_weights(self.log_std, network_config.weight_init)
            
            # Initialize log_std to specified value
            with torch.no_grad():
                if hasattr(self.log_std, 'network'):
                    # For MLPBlock
                    last_layer = list(self.log_std.network.children())[-1]
                    if isinstance(last_layer, nn.Linear):
                        last_layer.weight.fill_(0.0)
                        last_layer.bias.fill_(log_std_init)
    
    def forward(self, obs: torch.Tensor, deterministic: bool = False) -> torch.Tensor:
        """
        Forward pass to get actions
        
        Args:
            obs: Observations
            deterministic: Whether to return deterministic actions
            
        Returns:
            Actions
        """
        features = self.features_extractor(obs)
        mean_actions = self.mu(features)
        
        if deterministic:
            return torch.tanh(mean_actions)
        
        if self.use_sde:
            # SDE handling would go here
            raise NotImplementedError("SDE not implemented yet")
        else:
            log_std = self.log_std(features)
            log_std = torch.clamp(log_std, -20, 2)  # Clamp for stability
            
            std = torch.exp(log_std)
            normal = torch.distributions.Normal(mean_actions, std)
            
            # Reparameterization trick
            x_t = normal.rsample()
            action = torch.tanh(x_t)
            
            return action
    
    def action_log_prob(self, obs: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Get actions and their log probabilities
        
        Returns:
            Tuple of (actions, log_probabilities)
        """
        features = self.features_extractor(obs)
        mean_actions = self.mu(features)
        
        if self.use_sde:
            raise NotImplementedError("SDE not implemented yet")
        else:
            log_std = self.log_std(features)
            log_std = torch.clamp(log_std, -20, 2)
            
            std = torch.exp(log_std)
            normal = torch.distributions.Normal(mean_actions, std)
            
            # Reparameterization trick
            x_t = normal.rsample()
            action = torch.tanh(x_t)
            
            # Compute log probability
            log_prob = normal.log_prob(x_t)
            # Adjust for tanh transformation
            log_prob -= torch.log(1 - action.pow(2) + 1e-6)
            log_prob = log_prob.sum(dim=1, keepdim=True)
            
            return action, log_prob


class CustomCritic(nn.Module):
    """
    Custom Critic network for SAC
    """
    
    def __init__(
        self,
        observation_space: spaces.Space,
        action_space: spaces.Space,
        features_extractor: BaseFeaturesExtractor,
        features_dim: int,
        net_arch: List[int],
        activation_fn: Type[nn.Module] = nn.ReLU,
        normalize_images: bool = True,
        n_critics: int = 2,
        share_features_extractor: bool = True,
        network_config: Optional[NetworkConfig] = None,
        **kwargs,
    ):
        super().__init__()
        
        self.observation_space = observation_space
        self.action_space = action_space
        self.features_extractor = features_extractor
        self.features_dim = features_dim
        self.normalize_images = normalize_images
        self.n_critics = n_critics
        self.share_features_extractor = share_features_extractor
        
        if network_config is None:
            network_config = get_preset_config("basic_mlp")
        
        self.network_config = network_config
        
        # Get action dimension
        if isinstance(action_space, spaces.Box):
            action_dim = action_space.shape[0]
        else:
            raise ValueError(f"Unsupported action space: {action_space}")
        
        # Create critic networks
        self.q_networks = nn.ModuleList()
        
        for i in range(n_critics):
            q_net = NetworkFactory.create_network(
                config=network_config,
                input_dim=features_dim + action_dim,
                output_dim=1,
                network_role="critic"
            )
            self.q_networks.append(q_net)
        
        # Initialize weights
        for q_net in self.q_networks:
            NetworkFactory.initialize_weights(q_net, network_config.weight_init)
    
    def forward(self, obs: torch.Tensor, actions: torch.Tensor) -> Tuple[torch.Tensor, ...]:
        """
        Forward pass to get Q-values
        
        Args:
            obs: Observations
            actions: Actions
            
        Returns:
            Tuple of Q-values from each critic
        """
        features = self.features_extractor(obs)
        qvalue_input = torch.cat([features, actions], dim=1)
        
        q_values = []
        for q_net in self.q_networks:
            q_val = q_net(qvalue_input)
            q_values.append(q_val)
        
        return tuple(q_values)
    
    def q1_forward(self, obs: torch.Tensor, actions: torch.Tensor) -> torch.Tensor:
        """Forward pass through first critic only"""
        features = self.features_extractor(obs)
        qvalue_input = torch.cat([features, actions], dim=1)
        return self.q_networks[0](qvalue_input)


# Register the custom policy
try:
    from stable_baselines3.sac import SAC
    SAC.policy_aliases["CustomSACPolicy"] = CustomSACPolicy
except ImportError:
    pass  # SB3 not available, skip registration 