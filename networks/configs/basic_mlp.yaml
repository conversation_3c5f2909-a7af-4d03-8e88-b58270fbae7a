network_type: "mlp"
hidden_dims: [256, 256]
activation: "relu"
normalization: "layer_norm"
dropout_rate: 0.0
use_residual: false
use_attention: false
use_layer_norm: true
use_skip_connections: false
weight_init: "xavier"
shared_features: false

actor_config:
  hidden_dims: [256, 256]
  activation: "tanh"
  normalization: "layer_norm"
  dropout_rate: 0.0

critic_config:
  hidden_dims: [256, 256]
  activation: "relu"
  normalization: "layer_norm"
  dropout_rate: 0.0 