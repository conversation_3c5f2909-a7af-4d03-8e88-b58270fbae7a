"""
Custom Neural Network Architecture System for SAC Training

This package provides flexible and configurable neural network architectures
for Stable-Baselines3 SAC training, including:
- Modular network components (MLP, ResNet, Attention)
- Custom policy classes
- Configuration-driven network construction
- Advanced features like attention mechanisms and residual connections
"""

from .network_modules import M<PERSON><PERSON><PERSON>, ResidualBlock, EncoderModule
from .network_configs import NetworkConfig, get_preset_config
from .custom_policies import CustomSACPolicy
from .network_factory import NetworkFactory

__version__ = "0.1.0"

__all__ = [
    "MLPBlock",
    "ResidualBlock", 
    "EncoderModule",
    "NetworkConfig",
    "get_preset_config",
    "CustomSACPolicy",
    "NetworkFactory",
] 