"""
Network Factory for Dynamic Network Construction

This module provides the NetworkFactory class that can dynamically
create neural networks based on configuration specifications.
"""

import torch
import torch.nn as nn
from typing import Optional, Dict, Any, List, Tuple
import math

from .network_modules import (
    <PERSON><PERSON><PERSON><PERSON>, ResidualBlock, EncoderModule, AttentionBlock,
    ActivationType, NormalizationType
)
from .network_configs import NetworkConfig, NetworkType


class NetworkFactory:
    """
    Factory class for creating neural networks based on configuration
    
    This class handles the construction of different network architectures
    including MLP, ResNet, Attention-based, and hybrid networks.
    """
    
    @staticmethod
    def create_network(
        config: NetworkConfig,
        input_dim: int,
        output_dim: int,
        network_role: str = "actor"  # "actor", "critic", or "shared"
    ) -> nn.Module:
        """
        Create a neural network based on configuration
        
        Args:
            config: Network configuration
            input_dim: Input dimension
            output_dim: Output dimension
            network_role: Role of the network (actor, critic, or shared)
            
        Returns:
            PyTorch neural network module
        """
        
        # Get role-specific configuration
        if network_role == "actor" and config.actor_config:
            role_config = config.actor_config
        elif network_role == "critic" and config.critic_config:
            role_config = config.critic_config
        else:
            role_config = {
                "hidden_dims": config.hidden_dims,
                "activation": config.activation,
                "normalization": config.normalization,
                "dropout_rate": config.dropout_rate,
            }
        
        # Create network based on type
        if config.network_type == NetworkType.MLP:
            return NetworkFactory._create_mlp_network(
                input_dim=input_dim,
                output_dim=output_dim,
                config=config,
                role_config=role_config
            )
        
        elif config.network_type == NetworkType.RESIDUAL:
            return NetworkFactory._create_residual_network(
                input_dim=input_dim,
                output_dim=output_dim,
                config=config,
                role_config=role_config
            )
        
        elif config.network_type == NetworkType.ATTENTION:
            return NetworkFactory._create_attention_network(
                input_dim=input_dim,
                output_dim=output_dim,
                config=config,
                role_config=role_config
            )
        
        elif config.network_type == NetworkType.MULTI_MODAL:
            return NetworkFactory._create_multimodal_network(
                input_dim=input_dim,
                output_dim=output_dim,
                config=config,
                role_config=role_config
            )
        
        elif config.network_type == NetworkType.HYBRID:
            return NetworkFactory._create_hybrid_network(
                input_dim=input_dim,
                output_dim=output_dim,
                config=config,
                role_config=role_config
            )
        
        else:
            raise ValueError(f"Unsupported network type: {config.network_type}")
    
    @staticmethod
    def _create_mlp_network(
        input_dim: int,
        output_dim: int,
        config: NetworkConfig,
        role_config: Dict[str, Any]
    ) -> nn.Module:
        """Create a standard MLP network"""
        
        return MLPBlock(
            input_dim=input_dim,
            hidden_dims=role_config["hidden_dims"],
            output_dim=output_dim,
            activation=role_config["activation"],
            normalization=role_config["normalization"],
            dropout_rate=role_config["dropout_rate"],
            use_residual=config.use_skip_connections,
        )
    
    @staticmethod
    def _create_residual_network(
        input_dim: int,
        output_dim: int,
        config: NetworkConfig,
        role_config: Dict[str, Any]
    ) -> nn.Module:
        """Create a residual network with skip connections"""
        
        class ResidualNetwork(nn.Module):
            def __init__(self):
                super().__init__()
                
                hidden_dims = role_config["hidden_dims"]
                activation = role_config["activation"]
                normalization = role_config["normalization"]
                dropout_rate = role_config["dropout_rate"]
                
                # Input projection
                self.input_proj = nn.Linear(input_dim, hidden_dims[0])
                
                # Residual blocks
                self.residual_blocks = nn.ModuleList()
                for i in range(config.residual_blocks):
                    block = ResidualBlock(
                        dim=hidden_dims[0],
                        hidden_dim=hidden_dims[0] * 2,  # Expand in residual block
                        activation=activation,
                        normalization=normalization,
                        dropout_rate=dropout_rate,
                    )
                    self.residual_blocks.append(block)
                
                # Output layers
                if len(hidden_dims) > 1:
                    self.output_layers = MLPBlock(
                        input_dim=hidden_dims[0],
                        hidden_dims=hidden_dims[1:],
                        output_dim=output_dim,
                        activation=activation,
                        normalization=normalization,
                        dropout_rate=dropout_rate,
                    )
                else:
                    self.output_layers = nn.Linear(hidden_dims[0], output_dim)
            
            def forward(self, x):
                x = self.input_proj(x)
                
                for block in self.residual_blocks:
                    x = block(x)
                
                return self.output_layers(x)
        
        return ResidualNetwork()
    
    @staticmethod
    def _create_attention_network(
        input_dim: int,
        output_dim: int,
        config: NetworkConfig,
        role_config: Dict[str, Any]
    ) -> nn.Module:
        """Create an attention-based network"""
        
        class AttentionNetwork(nn.Module):
            def __init__(self):
                super().__init__()
                
                hidden_dims = role_config["hidden_dims"]
                activation = role_config["activation"]
                normalization = role_config["normalization"]
                dropout_rate = role_config["dropout_rate"]
                
                # Input embedding
                self.input_embed = nn.Linear(input_dim, hidden_dims[0])
                
                # Attention blocks
                self.attention_blocks = nn.ModuleList()
                for _ in range(2):  # Use 2 attention blocks
                    attn_block = AttentionBlock(
                        dim=hidden_dims[0],
                        num_heads=config.num_attention_heads,
                        dropout_rate=config.attention_dropout,
                    )
                    self.attention_blocks.append(attn_block)
                
                # Output MLP
                if len(hidden_dims) > 1:
                    self.output_mlp = MLPBlock(
                        input_dim=hidden_dims[0],
                        hidden_dims=hidden_dims[1:],
                        output_dim=output_dim,
                        activation=activation,
                        normalization=normalization,
                        dropout_rate=dropout_rate,
                    )
                else:
                    self.output_mlp = nn.Linear(hidden_dims[0], output_dim)
            
            def forward(self, x):
                x = self.input_embed(x)
                
                for attn_block in self.attention_blocks:
                    x = attn_block(x)
                
                return self.output_mlp(x)
        
        return AttentionNetwork()
    
    @staticmethod
    def _create_multimodal_network(
        input_dim: int,
        output_dim: int,
        config: NetworkConfig,
        role_config: Dict[str, Any]
    ) -> nn.Module:
        """Create a multi-modal network with separate input encoders"""
        
        if not config.input_encoders:
            # Fallback to standard MLP if no encoders specified
            return NetworkFactory._create_mlp_network(input_dim, output_dim, config, role_config)
        
        class MultiModalNetwork(nn.Module):
            def __init__(self):
                super().__init__()
                
                hidden_dims = role_config["hidden_dims"]
                activation = role_config["activation"]
                normalization = role_config["normalization"]
                dropout_rate = role_config["dropout_rate"]
                
                # Create input encoders
                self.encoders = nn.ModuleDict()
                total_encoded_dim = 0
                
                for encoder_config in config.input_encoders:
                    encoder_name = encoder_config["type"]
                    encoder_input_dim = encoder_config["dim"]
                    encoder_hidden_dims = encoder_config.get("hidden_dims", [64])
                    encoder_output_dim = encoder_hidden_dims[-1]
                    
                    encoder = MLPBlock(
                        input_dim=encoder_input_dim,
                        hidden_dims=encoder_hidden_dims[:-1],
                        output_dim=encoder_output_dim,
                        activation=activation,
                        normalization=normalization,
                        dropout_rate=dropout_rate,
                    )
                    
                    self.encoders[encoder_name] = encoder
                    total_encoded_dim += encoder_output_dim
                
                # Fusion and output layers
                self.fusion_mlp = MLPBlock(
                    input_dim=total_encoded_dim,
                    hidden_dims=hidden_dims,
                    output_dim=output_dim,
                    activation=activation,
                    normalization=normalization,
                    dropout_rate=dropout_rate,
                )
                
                # Store input dimension splits for automatic splitting
                self.input_splits = [enc["dim"] for enc in config.input_encoders]
            
            def forward(self, x):
                # Split input tensor based on encoder dimensions
                encoded_features = []
                start_idx = 0
                
                for i, (encoder_name, encoder) in enumerate(self.encoders.items()):
                    end_idx = start_idx + self.input_splits[i]
                    input_slice = x[..., start_idx:end_idx]
                    encoded = encoder(input_slice)
                    encoded_features.append(encoded)
                    start_idx = end_idx
                
                # Concatenate encoded features
                fused_features = torch.cat(encoded_features, dim=-1)
                
                return self.fusion_mlp(fused_features)
        
        return MultiModalNetwork()
    
    @staticmethod
    def _create_hybrid_network(
        input_dim: int,
        output_dim: int,
        config: NetworkConfig,
        role_config: Dict[str, Any]
    ) -> nn.Module:
        """Create a hybrid network combining multiple techniques"""
        
        class HybridNetwork(nn.Module):
            def __init__(self):
                super().__init__()
                
                hidden_dims = role_config["hidden_dims"]
                activation = role_config["activation"]
                normalization = role_config["normalization"]
                dropout_rate = role_config["dropout_rate"]
                
                # Input embedding
                self.input_embed = nn.Linear(input_dim, hidden_dims[0])
                
                # Attention blocks (if enabled)
                if config.use_attention:
                    self.attention_blocks = nn.ModuleList()
                    for _ in range(2):
                        attn_block = AttentionBlock(
                            dim=hidden_dims[0],
                            num_heads=config.num_attention_heads,
                            dropout_rate=config.attention_dropout,
                        )
                        self.attention_blocks.append(attn_block)
                else:
                    self.attention_blocks = None
                
                # Residual blocks (if enabled)
                if config.use_residual:
                    self.residual_blocks = nn.ModuleList()
                    for _ in range(config.residual_blocks):
                        res_block = ResidualBlock(
                            dim=hidden_dims[0],
                            activation=activation,
                            normalization=normalization,
                            dropout_rate=dropout_rate,
                        )
                        self.residual_blocks.append(res_block)
                else:
                    self.residual_blocks = None
                
                # Output layers
                if len(hidden_dims) > 1:
                    self.output_layers = MLPBlock(
                        input_dim=hidden_dims[0],
                        hidden_dims=hidden_dims[1:],
                        output_dim=output_dim,
                        activation=activation,
                        normalization=normalization,
                        dropout_rate=dropout_rate,
                    )
                else:
                    self.output_layers = nn.Linear(hidden_dims[0], output_dim)
            
            def forward(self, x):
                x = self.input_embed(x)
                
                # Apply attention blocks
                if self.attention_blocks:
                    for attn_block in self.attention_blocks:
                        x = attn_block(x)
                
                # Apply residual blocks
                if self.residual_blocks:
                    for res_block in self.residual_blocks:
                        x = res_block(x)
                
                return self.output_layers(x)
        
        return HybridNetwork()
    
    @staticmethod
    def count_parameters(model: nn.Module) -> int:
        """Count the number of trainable parameters in a model"""
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    @staticmethod
    def get_model_info(model: nn.Module) -> Dict[str, Any]:
        """Get detailed information about a model"""
        total_params = NetworkFactory.count_parameters(model)
        
        # Calculate model size in MB
        param_size = 0
        buffer_size = 0
        
        for param in model.parameters():
            param_size += param.nelement() * param.element_size()
        
        for buffer in model.buffers():
            buffer_size += buffer.nelement() * buffer.element_size()
        
        model_size_mb = (param_size + buffer_size) / 1024 / 1024
        
        return {
            "total_parameters": total_params,
            "trainable_parameters": total_params,
            "model_size_mb": model_size_mb,
            "architecture": str(model),
        }
    
    @staticmethod
    def initialize_weights(model: nn.Module, init_type: str = "xavier"):
        """Initialize model weights using specified strategy"""
        
        def init_func(m):
            if isinstance(m, (nn.Linear, nn.Conv1d, nn.Conv2d)):
                if init_type == "xavier":
                    nn.init.xavier_uniform_(m.weight)
                elif init_type == "kaiming":
                    nn.init.kaiming_uniform_(m.weight, nonlinearity='relu')
                elif init_type == "orthogonal":
                    nn.init.orthogonal_(m.weight)
                elif init_type == "normal":
                    nn.init.normal_(m.weight, 0, 0.02)
                else:
                    raise ValueError(f"Unknown initialization type: {init_type}")
                
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            
            elif isinstance(m, (nn.BatchNorm1d, nn.LayerNorm)):
                if hasattr(m, 'weight') and m.weight is not None:
                    nn.init.ones_(m.weight)
                if hasattr(m, 'bias') and m.bias is not None:
                    nn.init.zeros_(m.bias)
        
        model.apply(init_func) 