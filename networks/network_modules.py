"""
Basic Network Module Components

This module provides fundamental building blocks for neural networks:
- MLPBlock: Multi-layer perceptron with configurable activation and normalization
- ResidualBlock: Residual connection block for deeper networks
- EncoderModule: Input encoding module for different data types
"""

import torch
import torch.nn as nn
from typing import Optional, Union, List, Callable
from enum import Enum


class ActivationType(Enum):
    """Supported activation function types"""
    RELU = "relu"
    TANH = "tanh"
    SWISH = "swish"
    GELU = "gelu"
    ELU = "elu"
    LEAKY_RELU = "leaky_relu"


class NormalizationType(Enum):
    """Supported normalization types"""
    BATCH_NORM = "batch_norm"
    LAYER_NORM = "layer_norm"
    NONE = "none"


def get_activation_fn(activation: Union[str, ActivationType]) -> nn.Module:
    """Get activation function by name or type"""
    if isinstance(activation, str):
        activation = ActivationType(activation.lower())
    
    activation_map = {
        ActivationType.RELU: nn.ReLU(),
        ActivationType.TANH: nn.Tanh(),
        ActivationType.SWISH: nn.SiLU(),  # SiLU is equivalent to Swish
        ActivationType.GELU: nn.GELU(),
        ActivationType.ELU: nn.ELU(),
        ActivationType.LEAKY_RELU: nn.LeakyReLU(0.2),
    }
    
    return activation_map[activation]


def get_normalization_fn(norm_type: Union[str, NormalizationType], num_features: int) -> Optional[nn.Module]:
    """Get normalization layer by type"""
    if isinstance(norm_type, str):
        norm_type = NormalizationType(norm_type.lower())
    
    if norm_type == NormalizationType.BATCH_NORM:
        return nn.BatchNorm1d(num_features)
    elif norm_type == NormalizationType.LAYER_NORM:
        return nn.LayerNorm(num_features)
    else:
        return None


class MLPBlock(nn.Module):
    """
    Multi-Layer Perceptron Block with configurable architecture
    
    Features:
    - Configurable number of layers and hidden sizes
    - Multiple activation function options
    - Optional normalization (BatchNorm, LayerNorm)
    - Optional dropout
    - Residual connections for deeper networks
    """
    
    def __init__(
        self,
        input_dim: int,
        hidden_dims: List[int],
        output_dim: int,
        activation: Union[str, ActivationType] = ActivationType.RELU,
        normalization: Union[str, NormalizationType] = NormalizationType.NONE,
        dropout_rate: float = 0.0,
        use_residual: bool = False,
        final_activation: bool = False,
    ):
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dims = hidden_dims
        self.output_dim = output_dim
        self.use_residual = use_residual and (input_dim == output_dim)
        
        # Build layers
        layers = []
        prev_dim = input_dim
        
        # Hidden layers
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            
            # Add normalization
            norm_layer = get_normalization_fn(normalization, hidden_dim)
            if norm_layer is not None:
                layers.append(norm_layer)
            
            # Add activation
            layers.append(get_activation_fn(activation))
            
            # Add dropout
            if dropout_rate > 0:
                layers.append(nn.Dropout(dropout_rate))
            
            prev_dim = hidden_dim
        
        # Output layer
        layers.append(nn.Linear(prev_dim, output_dim))
        
        # Final activation (optional)
        if final_activation:
            layers.append(get_activation_fn(activation))
        
        self.network = nn.Sequential(*layers)
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize network weights using Xavier initialization"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through the MLP block"""
        out = self.network(x)
        
        # Add residual connection if enabled and dimensions match
        if self.use_residual:
            out = out + x
        
        return out


class ResidualBlock(nn.Module):
    """
    Residual Block with skip connections
    
    Implements: output = activation(MLP(x) + x)
    """
    
    def __init__(
        self,
        dim: int,
        hidden_dim: Optional[int] = None,
        activation: Union[str, ActivationType] = ActivationType.RELU,
        normalization: Union[str, NormalizationType] = NormalizationType.LAYER_NORM,
        dropout_rate: float = 0.0,
    ):
        super().__init__()
        
        if hidden_dim is None:
            hidden_dim = dim
        
        self.mlp = MLPBlock(
            input_dim=dim,
            hidden_dims=[hidden_dim],
            output_dim=dim,
            activation=activation,
            normalization=normalization,
            dropout_rate=dropout_rate,
            use_residual=False,  # We handle residual manually
            final_activation=False,
        )
        
        self.activation = get_activation_fn(activation)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass with residual connection"""
        return self.activation(self.mlp(x) + x)


class EncoderModule(nn.Module):
    """
    Input Encoder Module for different data types
    
    Supports:
    - Vector inputs (joint states, forces, etc.)
    - Sequence inputs with optional temporal encoding
    - Multi-modal input fusion
    """
    
    def __init__(
        self,
        input_dims: Union[int, List[int]],
        output_dim: int,
        encoder_type: str = "mlp",
        hidden_dims: Optional[List[int]] = None,
        activation: Union[str, ActivationType] = ActivationType.RELU,
        normalization: Union[str, NormalizationType] = NormalizationType.LAYER_NORM,
        dropout_rate: float = 0.0,
    ):
        super().__init__()
        
        self.encoder_type = encoder_type.lower()
        
        if isinstance(input_dims, int):
            input_dims = [input_dims]
        
        self.input_dims = input_dims
        self.output_dim = output_dim
        
        if hidden_dims is None:
            hidden_dims = [256, 128]
        
        if self.encoder_type == "mlp":
            # Simple MLP encoder
            total_input_dim = sum(input_dims)
            self.encoder = MLPBlock(
                input_dim=total_input_dim,
                hidden_dims=hidden_dims,
                output_dim=output_dim,
                activation=activation,
                normalization=normalization,
                dropout_rate=dropout_rate,
            )
        
        elif self.encoder_type == "multi_modal":
            # Multi-modal encoder with separate processing for each input
            self.encoders = nn.ModuleList()
            
            for input_dim in input_dims:
                encoder = MLPBlock(
                    input_dim=input_dim,
                    hidden_dims=[hidden_dims[0] // len(input_dims)],
                    output_dim=hidden_dims[0] // len(input_dims),
                    activation=activation,
                    normalization=normalization,
                    dropout_rate=dropout_rate,
                )
                self.encoders.append(encoder)
            
            # Fusion layer
            self.fusion = MLPBlock(
                input_dim=hidden_dims[0],
                hidden_dims=hidden_dims[1:],
                output_dim=output_dim,
                activation=activation,
                normalization=normalization,
                dropout_rate=dropout_rate,
            )
        
        else:
            raise ValueError(f"Unsupported encoder type: {encoder_type}")
    
    def forward(self, x: Union[torch.Tensor, List[torch.Tensor]]) -> torch.Tensor:
        """Forward pass through the encoder"""
        
        if self.encoder_type == "mlp":
            if isinstance(x, list):
                x = torch.cat(x, dim=-1)
            return self.encoder(x)
        
        elif self.encoder_type == "multi_modal":
            if not isinstance(x, list):
                # Split input tensor based on input dimensions
                x_list = []
                start_idx = 0
                for input_dim in self.input_dims:
                    x_list.append(x[..., start_idx:start_idx + input_dim])
                    start_idx += input_dim
                x = x_list
            
            # Process each input separately
            encoded_features = []
            for i, encoder in enumerate(self.encoders):
                encoded_features.append(encoder(x[i]))
            
            # Concatenate and fuse
            fused_features = torch.cat(encoded_features, dim=-1)
            return self.fusion(fused_features)


class AttentionBlock(nn.Module):
    """
    Simple Self-Attention Block
    
    Implements scaled dot-product attention for feature enhancement
    """
    
    def __init__(
        self,
        dim: int,
        num_heads: int = 8,
        dropout_rate: float = 0.1,
    ):
        super().__init__()
        
        assert dim % num_heads == 0, f"Dimension {dim} must be divisible by num_heads {num_heads}"
        
        self.dim = dim
        self.num_heads = num_heads
        self.head_dim = dim // num_heads
        self.scale = self.head_dim ** -0.5
        
        self.qkv = nn.Linear(dim, dim * 3, bias=False)
        self.proj = nn.Linear(dim, dim)
        self.dropout = nn.Dropout(dropout_rate)
        
        self.norm = nn.LayerNorm(dim)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through attention block
        
        Args:
            x: Input tensor of shape (batch_size, seq_len, dim) or (batch_size, dim)
        
        Returns:
            Output tensor with same shape as input
        """
        # Handle 2D input (batch_size, dim) by adding sequence dimension
        if x.dim() == 2:
            x = x.unsqueeze(1)  # (batch_size, 1, dim)
            squeeze_output = True
        else:
            squeeze_output = False
        
        batch_size, seq_len, dim = x.shape
        
        # Generate Q, K, V
        qkv = self.qkv(x).reshape(batch_size, seq_len, 3, self.num_heads, self.head_dim)
        qkv = qkv.permute(2, 0, 3, 1, 4)  # (3, batch_size, num_heads, seq_len, head_dim)
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        # Compute attention
        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.dropout(attn)
        
        # Apply attention to values
        out = (attn @ v).transpose(1, 2).reshape(batch_size, seq_len, dim)
        out = self.proj(out)
        
        # Residual connection and normalization
        out = self.norm(x + out)
        
        # Remove sequence dimension if it was added
        if squeeze_output:
            out = out.squeeze(1)
        
        return out 