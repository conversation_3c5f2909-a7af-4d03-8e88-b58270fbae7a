"""Configuration for objects in the scene."""

from __future__ import annotations

import isaaclab.sim as sim_utils
from isaaclab.assets import RigidObjectCfg

# Configuration for the target sphere
SphereObjectCfg = RigidObjectCfg(
    prim_path="/World/envs/env_.*/SphereObject", # Ensure this matches TARGET_SPHERE_PRIM_PATH in hand config
    spawn=sim_utils.SphereCfg(
        radius=0.05, # Example radius, adjust as needed
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            solver_position_iteration_count=8,
            solver_velocity_iteration_count=0,
            enable_gyroscopic_forces=True,
            max_depenetration_velocity=10.0,
            kinematic_enabled=False,
            disable_gravity=False,
        ),
        mass_props=sim_utils.MassPropertiesCfg(
            mass=1 # Example mass, adjust as needed
        ),
        collision_props=sim_utils.CollisionPropertiesCfg(
            collision_enabled=True,
            contact_offset=0.005,
            rest_offset=0.0,
        ),
        physics_material=sim_utils.RigidBodyMaterialCfg(
            static_friction=0.8,
            dynamic_friction=0.7,
            restitution=0.1,
        ),
        visual_material=sim_utils.PreviewSurfaceCfg(
            diffuse_color=(0.2, 0.2, 0.8), # Blue color
            metallic=0.1,
            roughness=0.5,
        )
    ),
    init_state=RigidObjectCfg.InitialStateCfg(
        pos=(0.0, 0.0, 0.0), # Initial position above the hand (X=0, Y=0, Z=0.6)
        rot=(0.0, 0.0, 0.0, 1.0),
        lin_vel=(0.0, 0.0, 0.0),
        ang_vel=(0.0, 0.0, 0.0),
    )
) 