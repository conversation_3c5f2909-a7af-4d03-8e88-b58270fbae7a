"""Configuration for the Inspire Hand Right.

Source USD: inspire_hand_r.usd (Expected)
"""

from __future__ import annotations

import isaaclab.sim as sim_utils
from isaaclab.actuators import ImplicitActuatorCfg
from isaaclab.assets import ArticulationCfg
from isaaclab.sensors import ContactSensorCfg
# Import necessary config for UrdfFileCfg joint_drive
from isaaclab.sim.converters import UrdfConverterCfg
# Potentially needed later if URDF path is relative to a package
# from omni.isaac.core.utils.extensions import get_extension_path_from_name

# Define the path to the USD file
import os
CURRENT_PATH = os.path.dirname(os.path.abspath(__file__))
# Assuming USD file is also in the project root like the URDF was
INSPIRE_HAND_USD_PATH = os.path.abspath(os.path.join(CURRENT_PATH, "../inspire_hand_r/inspire_hand_r.usd")) 
INSPIRE_HAND_URDF_PATH = os.path.abspath(os.path.join(CURRENT_PATH, "../inspire_hand_r.urdf")) 
# Placeholder List of actuated joint names - NEEDS TO BE FILLED FROM URDF
INSPIRE_HAND_ACTUATED_JOINT_NAMES = [
    # Thumb
    "R_thumb_MCP_joint1",
    "R_thumb_MCP_joint2",
    "R_thumb_PIP_joint",
    "R_thumb_DIP_joint",
    # Index
    "R_index_MCP_joint",
    "R_index_DIP_joint",
    # Middle
    "R_middle_MCP_joint",
    "R_middle_DIP_joint",
    # Ring
    "R_ring_MCP_joint",
    "R_ring_DIP_joint",
    # Pinky
    "R_pinky_MCP_joint",
    "R_pinky_DIP_joint",
] # Verified from inspire_hand_r.urdf (assuming USD uses same names)

# Placeholder List of distal link names for sensors - NEEDS TO BE FILLED FROM URDF
INSPIRE_HAND_FINGERTIP_LINK_NAMES = [
    "R_thumb_distal",
    "R_index_distal",
    "R_middle_distal",
    "R_ring_distal",
    "R_pinky_distal",
] # Verified from inspire_hand_r.urdf (assuming USD uses same names)


# Default stiffness and damping (will be overridden by RL agent actions)
# These values are placeholders and likely need tuning.
DEFAULT_STIFFNESS = 100.0
DEFAULT_DAMPING = 10.0

# Prim path for the target sphere (used for sensor filtering)
# This needs to match the prim path defined in the environment setup (Task 5)
TARGET_SPHERE_PRIM_PATH = "/World/envs/env_.*/SphereObject" # Example, adjust if needed

InspireHandRightCfg = ArticulationCfg(
    # spawn=sim_utils.UsdFileCfg(
    spawn=sim_utils.UsdFileCfg(
        usd_path=INSPIRE_HAND_USD_PATH,
        activate_contact_sensors=True, # Sensors will be added separately via sensors dict

        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=True,
            retain_accelerations=False,
            linear_damping=0.0,
            angular_damping=0.0,
            max_linear_velocity=1000.0,
            max_angular_velocity=1000.0,
            max_depenetration_velocity=100.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=8, # Adjusted for potentially complex hand contacts
            solver_velocity_iteration_count=0,
            sleep_threshold=0.005,
            stabilization_threshold=0.001,
        ),
        # For USD, might need to explicitly set physics material if not defined in USD
        # physics_material=sim_utils.PhysicsMaterialCfg(...),
    ),
    actuators={
        "inspire_hand_actuators": ImplicitActuatorCfg(
            joint_names_expr=INSPIRE_HAND_ACTUATED_JOINT_NAMES,
            effort_limit_sim=100.0, # Renamed from effort_limit
            velocity_limit_sim=100.0, # Renamed from velocity_limit
            stiffness=DEFAULT_STIFFNESS,
            damping=DEFAULT_DAMPING,
        )
    },
    soft_joint_pos_limit_factor=0.95, # Apply soft limits slightly below URDF limits
) 