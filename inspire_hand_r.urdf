<?xml version="1.0" encoding="utf-8"?>
<!-- This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>) 
     Commit Version: 1.6.0-4-g7f85cfe  Build Version: 1.6.7995.38578
     For more information, please see http://wiki.ros.org/sw_urdf_exporter -->
<robot
  name="inspire_hand_r">
  
  <link name="world"/>
  <joint name="fixed" type="fixed">
  <origin
   xyz="0.5 0.5 0.5"
   rpy="-1.5707963 0 0" />
  <parent link="world"/>
  <child link="R_hand_base_link"/>
  </joint>
  
  <link
    name="R_hand_base_link">
    <inertial>
      <origin
        xyz="-0.076223 -0.01701 0.24616"
        rpy="0 0 0" />
      <mass
        value="0.14143" />
      <inertia
        ixx="7.6663E-05"
        ixy="1.6551E-06"
        ixz="1.7709E-06"
        iyy="8.3832E-05"
        iyz="-2.1711E-06"
        izz="0.00012281" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_hand_base_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_hand_base_link.STL" />
      </geometry>
    </collision>
  </link>
  <link
    name="R_thumb_proximal_base">
    <inertial>
      <origin
        xyz="-0.0048772 -0.010163 0.00044069"
        rpy="0 0 0" />
      <mass
        value="0.0018869" />
      <inertia
        ixx="5.2002E-08"
        ixy="4.8353E-09"
        ixz="-6.6441E-09"
        iyy="6.7433E-08"
        iyz="5.4472E-10"
        izz="8.5319E-08" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_thumb_proximal_base.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_thumb_proximal_base.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="R_thumb_MCP_joint1"
    type="revolute">
    <origin
      xyz="-0.098633 -0.23173 -0.011364"
      rpy="1.5708 0 -0.28289" />
    <parent
      link="R_hand_base_link" />
    <child
      link="R_thumb_proximal_base" />
    <axis
      xyz="0 -1 0" />
    <limit
      lower="0.2"
      upper="1.3"
      effort="50"
      velocity="1" />
  </joint>
  <link
    name="R_thumb_proximal">
    <inertial>
      <origin
        xyz="-3.8592E-05 0.02533 -0.0017007"
        rpy="0 0 0" />
      <mass
        value="0.0066075" />
      <inertia
        ixx="2.786E-06"
        ixy="1.673E-10"
        ixz="-1.3436E-09"
        iyy="8.9422E-07"
        iyz="-2.1394E-07"
        izz="2.4097E-06" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_thumb_proximal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_thumb_proximal.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="R_thumb_MCP_joint2"
    type="revolute">
    <origin
      xyz="-0.0115 -0.011843 0"
      rpy="0.97599 1.5708 0" />
    <parent
      link="R_thumb_proximal_base" />
    <child
      link="R_thumb_proximal" />
    <axis
      xyz="-1 0 0" />
    <limit
      lower="1.3"
      upper="1.8"
      effort="50"
      velocity="1" />
  </joint>
  <link
    name="R_thumb_intermediate">
    <inertial>
      <origin
        xyz="2.8437E-07 0.0072526 -0.0064293"
        rpy="0 0 0" />
      <mass
        value="0.0037847" />
      <inertia
        ixx="4.6531E-07"
        ixy="-3.454E-12"
        ixz="3.4344E-12"
        iyy="2.6858E-07"
        iyz="-6.414E-08"
        izz="4.2517E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_thumb_intermediate.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_thumb_intermediate.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="R_thumb_PIP_joint"
    type="revolute">
    <origin
      xyz="0 0.055863 0.0039241"
      rpy="-0.038299 0 0" />
    <parent
      link="R_thumb_proximal" />
    <child
      link="R_thumb_intermediate" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="0"
      upper="1"
      effort="50"
      velocity="1" />
  </joint>
  <link
    name="R_thumb_distal">
    <inertial>
      <origin
        xyz="-3.3054E-06 0.010511 -0.00057863"
        rpy="0 0 0" />
      <mass
        value="0.0033441" />
      <inertia
        ixx="2.0026E-07"
        ixy="-1.4303E-10"
        ixz="-5.6945E-11"
        iyy="8.711E-08"
        iyz="-2.4416E-08"
        izz="1.8974E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_thumb_distal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_thumb_distal.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="R_thumb_DIP_joint"
    type="revolute">
    <origin
      xyz="0 0.022558 -0.0020717"
      rpy="-0.011642 0 0" />
    <parent
      link="R_thumb_intermediate" />
    <child
      link="R_thumb_distal" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="0"
      upper="1.2"
      effort="50"
      velocity="1" />
  </joint>
  <link
    name="R_index_proximal">
    <inertial>
      <origin
        xyz="0.0099008 0.011884 0.0016958"
        rpy="0 0 0" />
      <mass
        value="0.0042403" />
      <inertia
        ixx="6.9398E-07"
        ixy="-4.4846E-12"
        ixz="-3.0248E-12"
        iyy="2.3039E-07"
        iyz="-9.1779E-08"
        izz="6.434E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_index_proximal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_index_proximal.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="R_index_MCP_joint"
    type="revolute">
    <origin
      xyz="-0.094559 -0.24897 0.054025"
      rpy="1.3268 0.034907 3.1416" />
    <parent
      link="R_hand_base_link" />
    <child
      link="R_index_proximal" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="0"
      upper="1.7"
      effort="50"
      velocity="1" />
  </joint>
  <link
    name="R_index_distal">
    <inertial>
      <origin
        xyz="0.0083258 0.019591 0.0019565"
        rpy="0 0 0" />
      <mass
        value="0.0045683" />
      <inertia
        ixx="7.8179E-07"
        ixy="9.0022E-13"
        ixz="5.6729E-13"
        iyy="1.5635E-07"
        iyz="-2.1007E-07"
        izz="7.0084E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_index_distal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_index_distal.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="R_index_DIP_joint"
    type="revolute">
    <origin
      xyz="0.001575 0.030445 0.010275"
      rpy="0.044288 0 0" />
    <parent
      link="R_index_proximal" />
    <child
      link="R_index_distal" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="0"
      upper="1.6"
      effort="50"
      velocity="1" />
  </joint>
  <link
    name="R_middle_proximal">
    <inertial>
      <origin
        xyz="0.008065 0.011884 0.0016958"
        rpy="0 0 0" />
      <mass
        value="0.0042403" />
      <inertia
        ixx="6.9397E-07"
        ixy="-6.0276E-12"
        ixz="-3.3971E-12"
        iyy="2.3039E-07"
        iyz="-9.1777E-08"
        izz="6.4339E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_middle_proximal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_middle_proximal.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="R_middle_MCP_joint"
    type="revolute">
    <origin
      xyz="-0.077068 -0.24897 0.054042"
      rpy="1.3208 0 3.1416" />
    <parent
      link="R_hand_base_link" />
    <child
      link="R_middle_proximal" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="0"
      upper="1.7"
      effort="50"
      velocity="1" />
  </joint>
  <link
    name="R_middle_distal">
    <inertial>
      <origin
        xyz="0.0063978 0.020807 0.0018039"
        rpy="0 0 0" />
      <mass
        value="0.0050396" />
      <inertia
        ixx="9.8384E-07"
        ixy="5.3828E-12"
        ixz="3.4908E-12"
        iyy="1.7328E-07"
        iyz="-2.5594E-07"
        izz="8.914E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_middle_distal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_middle_distal.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="R_middle_DIP_joint"
    type="revolute">
    <origin
      xyz="0.0016673 0.030445 0.010275"
      rpy="0.071413 0 0" />
    <parent
      link="R_middle_proximal" />
    <child
      link="R_middle_distal" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="0"
      upper="1.6"
      effort="50"
      velocity="1" />
  </joint>
  <link
    name="R_ring_proximal">
    <inertial>
      <origin
        xyz="0.0080207 0.011884 0.0016959"
        rpy="0 0 0" />
      <mass
        value="0.0042403" />
      <inertia
        ixx="6.9397E-07"
        ixy="-6.088E-12"
        ixz="-3.1846E-12"
        iyy="2.3039E-07"
        iyz="-9.1776E-08"
        izz="6.4339E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_ring_proximal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_ring_proximal.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="R_ring_MCP_joint"
    type="revolute">
    <origin
      xyz="-0.057894 -0.24897 0.053119"
      rpy="1.3208 -0.05236 3.1416" />
    <parent
      link="R_hand_base_link" />
    <child
      link="R_ring_proximal" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="0"
      upper="1.7"
      effort="50"
      velocity="1" />
  </joint>
  <link
    name="R_ring_distal">
    <inertial>
      <origin
        xyz="0.0080209 0.019591 0.0019565"
        rpy="0 0 0" />
      <mass
        value="0.0045683" />
      <inertia
        ixx="7.8177E-07"
        ixy="4.448E-13"
        ixz="7.2441E-13"
        iyy="1.5635E-07"
        iyz="-2.1007E-07"
        izz="7.0082E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_ring_distal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_ring_distal.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="R_ring_DIP_joint"
    type="revolute">
    <origin
      xyz="0 0.030445 0.010275"
      rpy="0.039748 0 0" />
    <parent
      link="R_ring_proximal" />
    <child
      link="R_ring_distal" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="0"
      upper="1.6"
      effort="50"
      velocity="1" />
  </joint>
  <link
    name="R_pinky_proximal">
    <inertial>
      <origin
        xyz="0.0078887 0.011884 0.0016959"
        rpy="0 0 0" />
      <mass
        value="0.0042403" />
      <inertia
        ixx="6.9397E-07"
        ixy="-6.2993E-12"
        ixz="-3.3535E-12"
        iyy="2.3039E-07"
        iyz="-9.1776E-08"
        izz="6.4339E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_pinky_proximal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_pinky_proximal.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="R_pinky_MCP_joint"
    type="revolute">
    <origin
      xyz="-0.038882 -0.24897 0.051205"
      rpy="1.3208 -0.10472 -3.1416" />
    <parent
      link="R_hand_base_link" />
    <child
      link="R_pinky_proximal" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="0"
      upper="1.7"
      effort="50"
      velocity="1" />
  </joint>
  <link
    name="R_pinky_distal">
    <inertial>
      <origin
        xyz="0.0078857 0.016239 0.0022243"
        rpy="0 0 0" />
      <mass
        value="0.0035996" />
      <inertia
        ixx="4.4867E-07"
        ixy="6.675E-11"
        ixz="-1.9009E-11"
        iyy="1.2248E-07"
        iyz="-1.3511E-07"
        izz="3.8689E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_pinky_distal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="./meshes/R_pinky_distal.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="R_pinky_DIP_joint"
    type="revolute">
    <origin
      xyz="0 0.030445 0.010275"
      rpy="-0.037691 0 0" />
    <parent
      link="R_pinky_proximal" />
    <child
      link="R_pinky_distal" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="0"
      upper="1.6"
      effort="50"
      velocity="1" />
  </joint>
</robot>
