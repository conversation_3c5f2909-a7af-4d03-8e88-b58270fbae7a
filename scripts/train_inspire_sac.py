# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Script to train an Inspire Hand using Stable Baselines 3 (SAC)."""

"""Launch Isaac Sim simulator first."""

import argparse
import sys
import os
import random
from datetime import datetime

# Add project root to python path
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.abspath(os.path.join(SCRIPT_DIR, ".."))
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

from isaaclab.app import AppLauncher

# Add command line arguments
parser = argparse.ArgumentParser(description="Train Inspire Hand force control using Stable-Baselines3 (SAC).")
parser.add_argument("--video", action="store_true", default=False, help="Record videos during training.")
parser.add_argument("--video_length", type=int, default=200, help="Length of the recorded video (steps).")
parser.add_argument("--video_interval", type=int, default=2000, help="Interval between video recordings (steps).")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default="Isaac-InspireHandForce-Direct-v0", help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")
parser.add_argument("--max_iterations", type=int, default=None, help="RL policy training iterations (Total timesteps calculated based on this).")
parser.add_argument("--load_model", type=str, default=None, help="Path to a saved model to continue training.")
parser.add_argument("--network_config", type=str, default="basic_mlp", help="Network configuration preset or path to config file.")
parser.add_argument("--custom_policy", action="store_true", default=False, help="Use custom SAC policy with configurable networks.")
# Add AppLauncher CLI args
AppLauncher.add_app_launcher_args(parser)
# Parse arguments
args_cli, hydra_args = parser.parse_known_args()
# If video recording is enabled, force camera enablement
if args_cli.video:
    args_cli.enable_cameras = True

# Clean up sys.argv for Hydra
sys.argv = [sys.argv[0]] + hydra_args

# Launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""
import gymnasium as gym
import numpy as np

# stable-baselines3
from stable_baselines3 import SAC
from stable_baselines3.common.callbacks import CheckpointCallback
from stable_baselines3.common.logger import configure
from stable_baselines3.common.vec_env import VecNormalize

# Isaac Lab specifics
from isaaclab.utils.dict import print_dict
from isaaclab.utils.io import dump_pickle, dump_yaml

# Environment specific imports
import envs.inspire_hand_force_env # Make sure env is registered
from envs.inspire_hand_force_env import InspireHandForceEnvCfg
# Custom network imports
from networks import NetworkConfig, get_preset_config, CustomSACPolicy


# SB3 RL Framework wrapper
from isaaclab_rl.sb3 import Sb3VecEnvWrapper, process_sb3_cfg








def main():
    """Train with stable-baselines3 agent."""
    # Sample seed if not provided
    if args_cli.seed is None:
        args_cli.seed = random.randint(0, 10000)

    # Use default environment configuration
    env_cfg = InspireHandForceEnvCfg()

    # --- Load network configuration ---
    if args_cli.custom_policy:
        # Load custom network configuration
        if args_cli.network_config.endswith(('.yaml', '.yml', '.json')):
            # Load from file
            network_config = NetworkConfig.load(args_cli.network_config)
        else:
            # Load preset configuration
            network_config = get_preset_config(args_cli.network_config)
        
        print(f"[INFO] Using custom network configuration: {args_cli.network_config}")
        print(f"[INFO] Network type: {network_config.network_type.value}")
        print(f"[INFO] Hidden dimensions: {network_config.hidden_dims}")
        print(f"[INFO] Activation: {network_config.activation}")
    else:
        network_config = None

    # --- SAC Agent configuration 
    agent_cfg = {
        "policy": CustomSACPolicy if args_cli.custom_policy else "MlpPolicy",
        # SAC specific parameters (refer to SB3 SAC documentation)
        "buffer_size": 1_000_000,  # Replay buffer size
        "learning_rate": 3e-4,      # Learning rate (often needs tuning)
        "learning_starts": 10000,   # Steps before learning starts
        "batch_size": 256,        # Minibatch size
        "tau": 0.005,             # Soft update coefficient
        "gamma": 0.99,            # Discount factor
        "train_freq": 1,          # Update the model every n steps
        "gradient_steps": 1,      # How many gradient steps to perform per update
        "ent_coef": "auto",       # Entropy coefficient (auto mode recommended)
        # "target_update_interval": 1, # Often 1 for SAC
        "use_sde": False,         # Gaussian Stable DE (False for standard SAC)
        # General SB3 parameters
        "verbose": 1,
        "tensorboard_log": None, # Set later dynamically
        "seed": args_cli.seed,
        "device": args_cli.device if args_cli.device else "auto",
        # Training length (total_timesteps computed later)
        "total_timesteps": 10_000_000 # Default total steps, can be overridden by max_iterations
        # Normalization (can be added if needed, but start without)
        # "normalize_input": True,
        # "normalize_value": True,
        # "clip_obs": 10.0,
    }

    # Add custom network configuration if using custom policy
    if args_cli.custom_policy and network_config is not None:
        agent_cfg.update({
            "policy_kwargs": {
                "network_config": network_config,
                "features_dim": 256,  # Can be made configurable
            }
        })

    # Override configurations with command-line arguments
    if args_cli.num_envs is not None:
        env_cfg.scene.num_envs = args_cli.num_envs
    if args_cli.device is not None:
        env_cfg.sim.device = args_cli.device
        agent_cfg["device"] = args_cli.device

    # Calculate total timesteps based on max_iterations if provided
    # Note: SAC trains per step, not per rollout like PPO
    if args_cli.max_iterations is not None:
        agent_cfg["total_timesteps"] = args_cli.max_iterations # Treat max_iterations as total_timesteps for SAC

    # Set environment seed
    env_cfg.seed = agent_cfg["seed"]

    # --- Logging setup --- #
    log_root_path = os.path.join("logs", "sb3", args_cli.task)
    run_name = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    log_dir = os.path.join(log_root_path, run_name)
    agent_cfg["tensorboard_log"] = os.path.join(log_dir, "tb_logs")
    os.makedirs(log_dir, exist_ok=True)
    os.makedirs(os.path.join(log_dir, "params"), exist_ok=True)
    os.makedirs(agent_cfg["tensorboard_log"], exist_ok=True)
    print(f"[INFO] Logging experiment in: {log_dir}")

    # Dump configurations into log directory
    dump_yaml(os.path.join(log_dir, "params", "env.yaml"), env_cfg)
    dump_yaml(os.path.join(log_dir, "params", "agent.yaml"), agent_cfg)
    dump_pickle(os.path.join(log_dir, "params", "env.pkl"), env_cfg)
    dump_pickle(os.path.join(log_dir, "params", "agent.pkl"), agent_cfg)

    # Process agent configuration (removes extra keys)
    processed_agent_cfg = process_sb3_cfg(agent_cfg)

    # Read top-level agent settings
    policy_arch = processed_agent_cfg.pop("policy")
    total_timesteps = processed_agent_cfg.pop("total_timesteps")

    # --- Create environment --- #
    env = gym.make(args_cli.task, cfg=env_cfg, render_mode="rgb_array" if args_cli.video else None)
    # Wrap environment for Stable Baselines 3
    env = Sb3VecEnvWrapper(env)

    # -- Video recording -- #
    if args_cli.video:
        video_log_dir = os.path.join(log_dir, "videos", "train")
        os.makedirs(video_log_dir, exist_ok=True)
        video_kwargs = {
            "video_folder": video_log_dir,
            "step_trigger": lambda step: step % args_cli.video_interval == 0,
            "video_length": args_cli.video_length,
            "name_prefix": "train",
            "disable_logger": True,
        }
        print("[INFO] Recording videos during training.")
        print_dict(video_kwargs, nesting=4)
        env = gym.wrappers.RecordVideo(env, **video_kwargs)

    # -- Create or load agent -- #
    if args_cli.load_model:
        model_path = args_cli.load_model
        vec_normalize_path = os.path.join(os.path.dirname(model_path), "vec_normalize.pkl")
        if os.path.exists(vec_normalize_path):
            print(f"[INFO] Loading VecNormalize statistics from: {vec_normalize_path}")
            env = VecNormalize.load(vec_normalize_path, env)
        else:
             # Handle case where normalization might be needed but stats aren't saved
             # For simplicity, we assume if loading, VecNormalize was used and saved.
             # If using normalization without saving stats, create a new VecNormalize here.
             if processed_agent_cfg.get("normalize_input", False):
                 print("[WARNING] Loading model but VecNormalize stats not found. Creating new normalizer.")
                 env = VecNormalize(
                     env,
                     training=True,
                     norm_obs=processed_agent_cfg.pop("normalize_input", False),
                     norm_reward=processed_agent_cfg.pop("normalize_value", True),
                     clip_obs=processed_agent_cfg.pop("clip_obs", np.inf),
                     gamma=processed_agent_cfg["gamma"],
                     clip_reward=np.inf,
                 )
        print(f"[INFO] Loading saved model from: {model_path}")
        agent = SAC.load(model_path, env=env, **processed_agent_cfg)
        reset_num_timesteps = False # Do not reset timestep counter when loading
    else:
        # Create VecNormalize if configured (but typically handle normalization inside SAC/buffer)
        if processed_agent_cfg.get("normalize_input", False):
            print("[INFO] Using VecNormalize.")
            env = VecNormalize(
                env,
                training=True,
                norm_obs=processed_agent_cfg.pop("normalize_input", False),
                norm_reward=processed_agent_cfg.pop("normalize_value", True),
                clip_obs=processed_agent_cfg.pop("clip_obs", np.inf),
                gamma=processed_agent_cfg["gamma"],
                clip_reward=np.inf,
            )

        print("[INFO] Creating new SAC agent.")
        agent = SAC(policy_arch, env, **processed_agent_cfg)
        reset_num_timesteps = True
        
        # Print network information if using custom policy
        if args_cli.custom_policy and hasattr(agent.policy, 'get_network_info'):
            print("[INFO] Network Architecture Information:")
            network_info = agent.policy.get_network_info()
            for role, info in network_info.items():
                print(f"  {role.capitalize()}:")
                print(f"    Parameters: {info['total_parameters']:,}")
                print(f"    Size: {info['model_size_mb']:.2f} MB")

    # Configure logger
    new_logger = configure(log_dir, ["stdout", "tensorboard"])
    agent.set_logger(new_logger)

    # Callbacks
    checkpoint_callback = CheckpointCallback(
        save_freq=50000, # Save checkpoints less frequently for SAC? Check recommendations.
        save_path=log_dir,
        name_prefix="model",
        save_replay_buffer=True,
        save_vecnormalize=True,
        verbose=1
    )

    # --- Train agent --- #
    print(f"[INFO] Starting training for {total_timesteps} steps...")
    agent.learn(
        total_timesteps=total_timesteps,
        callback=checkpoint_callback,
        reset_num_timesteps=reset_num_timesteps
    )

    # --- Save final agent --- #
    final_model_path = os.path.join(log_dir, "model_final")
    print(f"[INFO] Saving final model to: {final_model_path}")
    agent.save(final_model_path)
    # Save VecNormalize statistics
    if isinstance(env, VecNormalize):
        env.save(os.path.join(log_dir, "vec_normalize_final.pkl"))

    # Close the environment
    print("[INFO] Closing environment...")
    env.close()
    print("[INFO] Training finished!")

if __name__ == "__main__":
    # Run the main function
    main()
    # Close the simulation application
    simulation_app.close() 