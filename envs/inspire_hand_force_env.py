"""RL Environment for Inspire Hand force control task."""

from __future__ import annotations

import gymnasium as gym
import torch
from collections import deque
import numpy as np


from isaaclab.envs import DirectRLEnv, DirectRLEnvCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.sim import SimulationCfg
from isaaclab.sim.spawners.from_files import GroundPlaneCfg, spawn_ground_plane
from isaaclab.utils import configclass
from isaaclab.utils.math import sample_uniform
from isaaclab.assets import Articulation, ArticulationCfg
from typing import Sequence
from isaaclab.assets import (
    Articulation,
    ArticulationCfg,
    RigidObject,
    RigidObjectCfg,
)
from isaaclab.managers import SceneEntityCfg
from dataclasses import MISSING
from isaaclab.sim.spawners.from_files import spawn_ground_plane
from isaaclab.sensors import ContactSensorCfg, ContactSensor
# Import asset configurations
from config.inspire_hand_cfg import InspireHandRightCfg, INSPIRE_HAND_FINGERTIP_LINK_NAMES, INSPIRE_HAND_ACTUATED_JOINT_NAMES
from config.objects_cfg import SphereObjectCfg
import isaaclab.sim as sim_utils
# Define paths used in the config
HAND_PRIM_PATH = "/World/envs/env_.*/InspireHand"
SPHERE_PRIM_PATH = "/World/envs/env_.*/SphereObject"

# Helper function to convert quaternion to 6D rotation representation
# Input shape: (..., 4) w, x, y, z
# Output shape: (..., 6)
def quat_to_rot_6d_pt(q: torch.Tensor) -> torch.Tensor:
    """Converts quaternions (w, x, y, z) to 6D rotation representation.
       Uses pytorch only.
    Args:
        q: Quaternions as tensor of shape (..., 4).
    Returns:
        6D rotation representation, tensor of shape (..., 6).
    """
    q = q / torch.norm(q, p=2, dim=-1, keepdim=True) # Normalize
    w, x, y, z = q[..., 0], q[..., 1], q[..., 2], q[..., 3]

    # Calculate rotation matrix elements
    # Ref: https://en.wikipedia.org/wiki/Rotation_matrix#Quaternion
    r11 = 1 - 2 * (y * y + z * z)
    r12 = 2 * (x * y - w * z)
    r13 = 2 * (x * z + w * y)

    r21 = 2 * (x * y + w * z)
    r22 = 1 - 2 * (x * x + z * z)
    r23 = 2 * (y * z - w * x)

    # r31 = 2 * (x * z - w * y)
    # r32 = 2 * (y * z + w * x)
    # r33 = 1 - 2 * (x * x + y * y)

    # Stack the first two columns (rotation matrix columns are axis representations)
    # Shape: (..., 6)
    return torch.stack((r11, r21, r12, r22, r13, r23), dim=-1)

@configclass
class InspireHandForceEnvCfg(DirectRLEnvCfg):
    """Configuration for the Inspire Hand force control environment."""
    # Env-specific settings
    num_frames_to_stack: int = 1 # Number of observations to stack
    decimation: int = 2 # Number of physics steps per RL step
    episode_length_s: float =10.0 # Max episode length in seconds
    action_scale: float = 1.0 # Scaling for raw actions [-1, 1]

    # --- Calculate dimensions directly in the config --- #
    # Hardcoded number of joints for now
    _num_hand_joints: int = 12
    # Base observation dim = joint_pos(12) + joint_vel(12) + stiffness(12) + damping(12) + sphere_pose_6d(9) + contact_forces(5*3=15) = 72
    _base_observation_dim: int = (_num_hand_joints * 4) + 9 + (5 * 3)
    # Final observation dim considering frame stacking - Assign to observation_space
    observation_space: int = _base_observation_dim * num_frames_to_stack # Renamed from num_observations
    # Action dim = pos_delta(12) + impedance(12) = 24 - Assign to action_space
    action_space: int = _num_hand_joints * 2 # Renamed from num_actions

    # --- Remove MISSING placeholders for base config fields --- #
    # (Keep commented out)
    # observation_space: gym.Space = MISSING
    # action_space: gym.Space = MISSING
    # num_observations: int = MISSING # Removed field
    # num_actions: int = MISSING # Removed field

    # Simulation settings
    sim: SimulationCfg = SimulationCfg(dt=1/120.0, render_interval=decimation)

    # Scene Configuration (Sensors defined below)
    scene: InteractiveSceneCfg = InteractiveSceneCfg(
        num_envs=512,
        env_spacing=2.0,
        replicate_physics=True,
    )

    # Asset Configurations
    print(f"[INFO]========================== InspireHandForceEnv: Spawned Inspire Hand at {HAND_PRIM_PATH}")
    inspire_hand_cfg: ArticulationCfg = InspireHandRightCfg.replace(
        prim_path=HAND_PRIM_PATH,
        init_state=ArticulationCfg.InitialStateCfg(
            pos=(0.0, 0.0, 1.0), # Raised Z to prevent clipping after rotation
            rot=(0.0, 1.0, 0.0, 0.0), # Rotated 180 deg around X to face palm up (wxyz)
            joint_pos={
                # Thumb - Updated default positions to be within limits
                "R_thumb_MCP_joint1": 0.2,  # Was 0.0, Lower limit is 0.2
                "R_thumb_MCP_joint2": 1.3,  # Was 0.0, Lower limit is 1.3
                "R_thumb_PIP_joint": 0.0,
                "R_thumb_DIP_joint": 0.0,
                # Index
                "R_index_MCP_joint": 0.0,
                "R_index_DIP_joint": 0.0,
                # Middle
                "R_middle_MCP_joint": 0.0,
                "R_middle_DIP_joint": 0.0,
                # Ring
                "R_ring_MCP_joint": 0.0,
                "R_ring_DIP_joint": 0.0,
                # Pinky
                "R_pinky_MCP_joint": 0.0,
                "R_pinky_DIP_joint": 0.0,
            },
            joint_vel={name: 0.0 for name in INSPIRE_HAND_ACTUATED_JOINT_NAMES},
        )
    )
    print(f"[INFO]========================== InspireHandForceEnv: Inspire Hand articulation: {inspire_hand_cfg}")
    sphere: RigidObjectCfg = SphereObjectCfg.replace(prim_path=SPHERE_PRIM_PATH, init_state=RigidObjectCfg.InitialStateCfg(
            pos=(0.425, -0.55, 0.5), rot=(0.0, 0.0, 0.0, 1.0)
        ))




    # -- Define Sensor configurations explicitly --
    contact_R_thumb_distal: ContactSensorCfg = ContactSensorCfg(
        prim_path=f"{HAND_PRIM_PATH}/R_thumb_distal",
        update_period=0.0,
        history_length=3,
        debug_vis=True,
        filter_prim_paths_expr=[SPHERE_PRIM_PATH]
    )
    contact_R_index_distal: ContactSensorCfg = ContactSensorCfg(
        prim_path=f"{HAND_PRIM_PATH}/R_index_distal",
        update_period=0.0,
        history_length=3,
        debug_vis=True,
        filter_prim_paths_expr=[SPHERE_PRIM_PATH]
    )
    contact_R_middle_distal: ContactSensorCfg = ContactSensorCfg(
        prim_path=f"{HAND_PRIM_PATH}/R_middle_distal",
        update_period=0.0,
        history_length=3,
        debug_vis=True,
        filter_prim_paths_expr=[SPHERE_PRIM_PATH]
    )
    contact_R_ring_distal: ContactSensorCfg = ContactSensorCfg(
        prim_path=f"{HAND_PRIM_PATH}/R_ring_distal",
        update_period=0.0,
        history_length=3,
        debug_vis=True,
        filter_prim_paths_expr=[SPHERE_PRIM_PATH]
    )
    contact_R_pinky_distal: ContactSensorCfg = ContactSensorCfg(
        prim_path=f"{HAND_PRIM_PATH}/R_pinky_distal",
        update_period=0.0,
        history_length=3,
        debug_vis=True,
        filter_prim_paths_expr=[SPHERE_PRIM_PATH]
    )
    # -- End explicit sensor definitions --

    rew_force_error_scale: float = -1.0 # Penalty for force error
    rew_action_rate_scale: float = -0.01 # Penalty for large action changes
    rew_joint_vel_scale: float = -0.005 # Penalty for high joint velocities
    rew_alive_scale: float = 0.1 # Small reward for staying alive
    rew_terminate_scale: float = -2.0 # Penalty for early termination (e.g., dropping)

    # Grasping reward components
    rew_object_distance_scale: float = 2.0 # Reward for object proximity to palm
    rew_multi_contact_scale: float = 1.0 # Reward for multiple finger contacts
    rew_object_stability_scale: float = 0.5 # Reward for object velocity stability
    rew_finger_distribution_scale: float = 0.3 # Reward for finger distribution around object
    rew_grasp_bonus_scale: float = 5.0 # Bonus reward for successful grasp

    # Termination conditions
    termination_force_threshold: float = 100.0 # Max force allowed before termination
    termination_drop_distance: float = 1 # Max distance sphere can be from hand

    # Joint coupling parameters
    finger_coupling_ratio: float = 0.8 # Ratio for DIP = MCP * ratio (non-thumb fingers)
    thumb_coupling_enabled: bool = True # Enable thumb DIP-PIP coupling
    coupling_debug: bool = False # Enable coupling debug logging

    # Impedance control parameters
    impedance_debug: bool = False # Enable impedance debug logging

    # Personalized force reward parameters
    finger_target_forces: list[float] = [5.0, 4.0, 4.0, 3.0, 2.0] # Individual target forces [thumb, index, middle, ring, pinky]
    finger_force_weights: list[float] = [1.0, 1.0, 1.0, 0.8, 0.6] # Individual error weights [thumb, index, middle, ring, pinky]
    force_error_aggregation: str = "weighted_sum" # Method for combining finger errors: "weighted_sum", "mean", "max"
    sensor_validation_debug: bool = True # Enable sensor validation debug logging

    # TensorBoard logging parameters
    tb_log_freq: int = 100 # Frequency of TensorBoard logging (steps)
    tb_log_forces: bool = True # Enable force data logging to TensorBoard
    tb_log_detailed: bool = False # Enable detailed force statistics (histograms, distributions)



class InspireHandForceEnv(DirectRLEnv):
    """RL Environment for controlling Inspire Hand fingertip forces."""

    cfg: InspireHandForceEnvCfg

    def __init__(self, cfg: InspireHandForceEnvCfg, render_mode: str | None = None, **kwargs):
        # Initialize the parent class FIRST to set self.cfg
        super().__init__(cfg, render_mode, **kwargs)

        # Dimensions are now pre-calculated in self.cfg
        # Remove dimension calculation code:
        # num_hand_joints = 12
        # self.cfg.action_space_dim = num_hand_joints * 3
        # self.cfg.base_observation_dim = (num_hand_joints * 2) + 9 + (5 * 3)
        # self.cfg.observation_space_dim = self.cfg.base_observation_dim * self.cfg.num_frames_to_stack

        # -- Setup observation history deque --
        # Use self.cfg.num_frames_to_stack (which was already correct)
        self._obs_history = [deque(maxlen=self.cfg.num_frames_to_stack) for _ in range(self.num_envs)]

        # -- Define action space using self.cfg.action_space (int) --
        self.action_space = gym.spaces.Box(
            low=-1.0, high=1.0, shape=(self.cfg.action_space,), dtype=np.float32 # Use cfg.action_space for shape
        )
        # Define ranges for actions (position delta and impedance)
        self.action_joint_pos_scale = self.cfg.action_scale
        self.action_impedance_scale = 100.0 # Impedance range [0, 100]
        # Action centers
        self.action_joint_pos_center = 0.0
        self.action_impedance_center = self.action_impedance_scale / 2.0

        # -- Define observation space using self.cfg.observation_space (int) --
        self.observation_space = gym.spaces.Box(
            low=-np.inf, high=np.inf, shape=(self.cfg.observation_space,), dtype=np.float32 # Use cfg.observation_space for shape
        )
        # Print using the correct config attributes
        print(f"[INFO] InspireHandForceEnv: Observation space dim: {self.cfg.observation_space}")
        print(f"[INFO] InspireHandForceEnv: Action space dim: {self.cfg.action_space}")

        # Validate and print coupling configuration
        self._validate_coupling_config()
        
        # Validate and print impedance configuration
        self._validate_impedance_config()
        
        # Validate sensor positions and configuration
        self._validate_sensor_configuration()
        
        # Validate TensorBoard logging configuration
        self._validate_tensorboard_config()

        # Initialize TensorBoard logger reference (will be set by training script)
        self.tb_logger = None

        # Buffers
        # Remove joint_pos and joint_vel initializations, access directly from self.inspire_hand.data when needed
        # self.joint_pos = None
        # self.joint_vel = None
        # Use self.cfg.action_space for buffer size
        self.actions_buf = torch.zeros((self.num_envs, self.cfg.action_space), device=self.device)
        self.last_actions = torch.zeros_like(self.actions_buf)

    def _setup_scene(self) -> None:
        """Setup the scene for the environment."""
        # Spawn Inspire Hand and add it to the scene's articulations dictionary
        self.inspire_hand = Articulation(self.cfg.inspire_hand_cfg)
        print(f"====================================[INFO] InspireHandForceEnv: Inspire Hand articulation: {self.inspire_hand}")
        self.scene.articulations["inspire_hand"] = self.inspire_hand
        print(f"====================================[INFO] InspireHandForceEnv: Inspire Hand scene articulation: {self.scene.articulations}")
        spawn_ground_plane(prim_path="/World/ground", cfg=GroundPlaneCfg())

        self.scene.clone_environments(copy_from_source=False)

        # Spawn Sphere Object and add it to the scene's rigid_objects dictionary
        self.sphere = RigidObject(self.cfg.sphere)
        self.scene.rigid_objects["sphere"] = self.sphere

        # Spawn Ground Plane using the specific spawner function
        # self.scene.add_ground_plane(cfg=self.cfg.ground) # Incorrect method


        # Ensure the DomeLightCfg correctly defines its .func attribute (it should by default)
        light_cfg = sim_utils.DomeLightCfg(intensity=2000.0, color=(0.75, 0.75, 0.75))
        light_cfg.func("/World/Light", light_cfg)

        # -- Initialize Sensors --
        # Define the names of the sensor configurations in the Cfg class
        contact_sensor_cfg_names = [
            "contact_R_thumb_distal",
            "contact_R_index_distal",
            "contact_R_middle_distal",
            "contact_R_ring_distal",
            "contact_R_pinky_distal",
        ]
        print("[INFO] Initializing contact sensors...")
        for sensor_name in contact_sensor_cfg_names:
            try:
                # Get the sensor config from self.cfg
                sensor_cfg = getattr(self.cfg, sensor_name)
                # Create the sensor instance using the config
                sensor_instance = ContactSensor(sensor_cfg)
                # Store the sensor instance as a direct attribute of self
                setattr(self, sensor_name, sensor_instance)
                print(f"  [INFO] Initialized and stored sensor: {sensor_name}")
            except AttributeError:
                print(f"  [ERROR] Config attribute '{sensor_name}' not found in self.cfg. Skipping sensor initialization.")
            except Exception as e:
                print(f"  [ERROR] Failed to initialize sensor '{sensor_name}': {e}")

        # Call super AFTER creating all assets and sensors defined in this class's config
        # (Although DirectRLEnv's _setup_scene might be empty, it's good practice)
        # super()._setup_scene() # Check if DirectRLEnv has a _setup_scene to call


    def _pre_physics_step(self, actions: torch.Tensor) -> None:
        """Apply actions to the simulation."""
        # Store raw actions [-1, 1]
        self.actions_buf = actions.clone()

        # Reset environments that terminated
        env_ids = self.reset_terminated.nonzero(as_tuple=False).squeeze(-1)
        if len(env_ids) > 0:
            self._reset_idx(env_ids)


    def _validate_coupling_config(self) -> None:
        """Validate coupling configuration parameters and print info."""
        # Validate coupling ratio
        if not (0.0 <= self.cfg.finger_coupling_ratio <= 2.0):
            print(f"[WARNING] finger_coupling_ratio={self.cfg.finger_coupling_ratio} is outside recommended range [0.0, 2.0]")
        
        # Print coupling configuration
        print(f"[INFO] Joint Coupling Configuration:")
        print(f"  Finger coupling ratio: {self.cfg.finger_coupling_ratio}")
        print(f"  Thumb coupling enabled: {self.cfg.thumb_coupling_enabled}")
        print(f"  Coupling debug: {self.cfg.coupling_debug}")
        
        # Print joint coupling pairs that will be applied
        fingers = ['index', 'middle', 'ring', 'pinky']
        print(f"  Coupling pairs:")
        for finger in fingers:
            print(f"    {finger}: MCP -> DIP (ratio: {self.cfg.finger_coupling_ratio})")
        if self.cfg.thumb_coupling_enabled:
            print(f"    thumb: PIP -> DIP (ratio: {self.cfg.finger_coupling_ratio})")

    def _validate_impedance_config(self) -> None:
        """Validate impedance configuration parameters and print info."""
        # Print impedance configuration
        print(f"[INFO] Impedance Control Configuration:")
        print(f"  Action space: {self.cfg.action_space}D (pos_delta + impedance)")
        print(f"  Impedance scale: {self.action_impedance_scale}")
        print(f"  Impedance debug: {self.cfg.impedance_debug}")
        
        # Print conversion strategy
        print(f"  Impedance conversion strategy:")
        print(f"    Stiffness: proportional to impedance [0.1, {self.action_impedance_scale}]")
        print(f"    Damping: inversely related to impedance [1.0, 20.0]")
        print(f"    High impedance -> High stiffness, Low damping")
        print(f"    Low impedance -> Low stiffness, High damping")

    def _validate_sensor_configuration(self) -> None:
        """Validate sensor positions and configuration."""
        print(f"[INFO] Sensor Configuration Validation:")
        
        # Validate personalized force parameters
        if len(self.cfg.finger_target_forces) != 5:
            print(f"[ERROR] finger_target_forces must have 5 values, got {len(self.cfg.finger_target_forces)}")
        
        if len(self.cfg.finger_force_weights) != 5:
            print(f"[ERROR] finger_force_weights must have 5 values, got {len(self.cfg.finger_force_weights)}")
        
        # Print personalized force configuration
        finger_names = ["thumb", "index", "middle", "ring", "pinky"]
        print(f"  Personalized Force Configuration:")
        for i, finger in enumerate(finger_names):
            target_force = self.cfg.finger_target_forces[i] if i < len(self.cfg.finger_target_forces) else 0.0
            weight = self.cfg.finger_force_weights[i] if i < len(self.cfg.finger_force_weights) else 1.0
            print(f"    {finger}: target={target_force:.1f}N, weight={weight:.1f}")
        
        print(f"  Force error aggregation: {self.cfg.force_error_aggregation}")
        
        # Validate sensor existence and print positions
        contact_sensor_names = [
            "contact_R_thumb_distal",
            "contact_R_index_distal",
            "contact_R_middle_distal",
            "contact_R_ring_distal",
            "contact_R_pinky_distal",
        ]
        
        print(f"  Sensor Status:")
        for i, sensor_name in enumerate(contact_sensor_names):
            finger_name = finger_names[i]
            try:
                sensor = getattr(self, sensor_name)
                if isinstance(sensor, ContactSensor):
                    print(f"    {finger_name}: ✓ Sensor initialized successfully")
                    if self.cfg.sensor_validation_debug:
                        print(f"      Sensor config: {sensor.cfg.prim_path}")
                else:
                    print(f"    {finger_name}: ✗ Sensor exists but wrong type: {type(sensor)}")
            except AttributeError:
                print(f"    {finger_name}: ✗ Sensor not found: {sensor_name}")
        
        # Validate aggregation method
        valid_aggregations = ["weighted_sum", "mean", "max"]
        if self.cfg.force_error_aggregation not in valid_aggregations:
            print(f"[WARNING] Invalid force_error_aggregation: {self.cfg.force_error_aggregation}")
            print(f"  Valid options: {valid_aggregations}")

    def _validate_tensorboard_config(self) -> None:
        """Validate TensorBoard logging configuration parameters and print info."""
        print(f"[INFO] TensorBoard Logging Configuration:")
        print(f"  Log frequency (steps): {self.cfg.tb_log_freq}")
        print(f"  Log forces: {self.cfg.tb_log_forces}")
        print(f"  Log detailed forces: {self.cfg.tb_log_detailed}")
        if self.cfg.tb_log_freq > 0:
            print(f"  TensorBoard will be initialized and logged to.")
        else:
            print(f"  TensorBoard logging is disabled (tb_log_freq <= 0).")

    def set_tensorboard_logger(self, logger):
        """Set TensorBoard logger from training script.
        
        Args:
            logger: SB3 TensorBoard logger or compatible logger instance
        """
        self.tb_logger = logger
        print(f"[INFO] TensorBoard logger set: {type(logger)}")

    def _log_force_data_to_tensorboard(self, current_forces_magnitudes: list[torch.Tensor], 
                                      individual_force_errors: list[torch.Tensor],
                                      step: int) -> None:
        """Log force data to TensorBoard.
        
        Args:
            current_forces_magnitudes: List of force magnitude tensors for each finger
            individual_force_errors: List of force error tensors for each finger
            step: Current training step
        """
        if not self.cfg.tb_log_forces or self.tb_logger is None:
            return
        
        if step % self.cfg.tb_log_freq != 0:
            return
        
        finger_names = ["thumb", "index", "middle", "ring", "pinky"]
        
        # Log per-finger metrics
        for i, finger_name in enumerate(finger_names):
            if i < len(current_forces_magnitudes) and i < len(individual_force_errors):
                # Force magnitude statistics
                force_mean = torch.mean(current_forces_magnitudes[i]).item()
                force_std = torch.std(current_forces_magnitudes[i]).item()
                force_max = torch.max(current_forces_magnitudes[i]).item()
                force_min = torch.min(current_forces_magnitudes[i]).item()
                
                # Force error statistics
                error_mean = torch.mean(individual_force_errors[i]).item()
                error_std = torch.std(individual_force_errors[i]).item()
                
                # Target force for this finger
                target_force = self.cfg.finger_target_forces[i] if i < len(self.cfg.finger_target_forces) else 5.0
                
                # Log scalar metrics
                self.tb_logger.record(f"forces/{finger_name}/mean", force_mean, exclude="time")
                self.tb_logger.record(f"forces/{finger_name}/std", force_std, exclude="time")
                self.tb_logger.record(f"forces/{finger_name}/max", force_max, exclude="time")
                self.tb_logger.record(f"forces/{finger_name}/min", force_min, exclude="time")
                self.tb_logger.record(f"forces/{finger_name}/target", target_force, exclude="time")
                
                self.tb_logger.record(f"force_errors/{finger_name}/mean", error_mean, exclude="time")
                self.tb_logger.record(f"force_errors/{finger_name}/std", error_std, exclude="time")
                
                # Calculate force tracking accuracy (percentage within 10% of target)
                tolerance = 0.1 * target_force
                within_tolerance = torch.abs(current_forces_magnitudes[i] - target_force) <= tolerance
                accuracy = torch.mean(within_tolerance.float()).item()
                self.tb_logger.record(f"force_accuracy/{finger_name}", accuracy, exclude="time")
        
        # Log aggregate metrics
        all_forces = torch.cat(current_forces_magnitudes, dim=0)
        all_errors = torch.cat(individual_force_errors, dim=0)
        
        self.tb_logger.record("forces/overall/mean", torch.mean(all_forces).item(), exclude="time")
        self.tb_logger.record("forces/overall/std", torch.std(all_forces).item(), exclude="time")
        self.tb_logger.record("force_errors/overall/mean", torch.mean(all_errors).item(), exclude="time")
        self.tb_logger.record("force_errors/overall/std", torch.std(all_errors).item(), exclude="time")
        
        # Log contact statistics
        contact_threshold = 0.5
        contact_count = torch.zeros(self.num_envs, device=self.device)
        for force_mag in current_forces_magnitudes:
            contact_count += (force_mag > contact_threshold).float()
        
        avg_contacts = torch.mean(contact_count).item()
        self.tb_logger.record("contacts/average_fingers_in_contact", avg_contacts, exclude="time")
        self.tb_logger.record("contacts/percentage_envs_with_full_contact", 
                             torch.mean((contact_count >= 5).float()).item(), exclude="time")
        
        # Log detailed force histograms (if enabled)
        if self.cfg.tb_log_detailed:
            # Note: SB3 logger might not support histograms directly
            # This would need to be implemented with a custom logger or matplotlib
            pass

    def _apply_joint_coupling(self, target_joint_pos: torch.Tensor) -> torch.Tensor:
        """Apply joint coupling to target joint positions.
        
        Args:
            target_joint_pos: Target joint positions (num_envs, num_joints)
            
        Returns:
            Coupled target joint positions (num_envs, num_joints)
        """
        # Get joint names to find indices
        joint_names = self.inspire_hand.joint_names
        
        # Create mapping from joint names to indices
        joint_name_to_idx = {name: idx for idx, name in enumerate(joint_names)}
        
        # Apply coupling for each finger
        coupled_pos = target_joint_pos.clone()
        
        # Get joint limits for clamping
        joint_limits = self.inspire_hand.data.joint_limits
        
        # Non-thumb fingers: DIP = MCP * coupling_ratio
        fingers = ['index', 'middle', 'ring', 'pinky']
        for finger in fingers:
            mcp_name = f"R_{finger}_MCP_joint"
            dip_name = f"R_{finger}_DIP_joint"
            
            if mcp_name in joint_name_to_idx and dip_name in joint_name_to_idx:
                mcp_idx = joint_name_to_idx[mcp_name]
                dip_idx = joint_name_to_idx[dip_name]
                
                # Apply coupling: DIP = MCP * coupling_ratio
                coupled_dip_pos = coupled_pos[:, mcp_idx] * self.cfg.finger_coupling_ratio
                
                # Clamp to joint limits
                if joint_limits is not None:
                    coupled_dip_pos = torch.clamp(
                        coupled_dip_pos, 
                        min=joint_limits[dip_idx, 0], 
                        max=joint_limits[dip_idx, 1]
                    )
                
                coupled_pos[:, dip_idx] = coupled_dip_pos
                
                if self.cfg.coupling_debug:
                    print(f"[DEBUG] Finger {finger}: MCP({mcp_idx})={target_joint_pos[0, mcp_idx]:.3f} -> DIP({dip_idx})={coupled_pos[0, dip_idx]:.3f}")
        
        # Thumb: DIP controlled by PIP (if enabled)
        if self.cfg.thumb_coupling_enabled:
            pip_name = "R_thumb_PIP_joint"
            dip_name = "R_thumb_DIP_joint"
            
            if pip_name in joint_name_to_idx and dip_name in joint_name_to_idx:
                pip_idx = joint_name_to_idx[pip_name]
                dip_idx = joint_name_to_idx[dip_name]
                
                # For thumb, DIP follows PIP with same coupling ratio
                coupled_dip_pos = coupled_pos[:, pip_idx] * self.cfg.finger_coupling_ratio
                
                # Clamp to joint limits
                if joint_limits is not None:
                    coupled_dip_pos = torch.clamp(
                        coupled_dip_pos, 
                        min=joint_limits[dip_idx, 0], 
                        max=joint_limits[dip_idx, 1]
                    )
                
                coupled_pos[:, dip_idx] = coupled_dip_pos
                
                if self.cfg.coupling_debug:
                    print(f"[DEBUG] Thumb: PIP({pip_idx})={target_joint_pos[0, pip_idx]:.3f} -> DIP({dip_idx})={coupled_pos[0, dip_idx]:.3f}")
        
        return coupled_pos

    def _impedance_to_stiffness_damping(self, impedance_actions: torch.Tensor) -> tuple[torch.Tensor, torch.Tensor]:
        """Convert impedance actions to stiffness and damping values.
        
        Args:
            impedance_actions: Impedance actions (num_envs, num_joints) in range [-1, 1]
            
        Returns:
            Tuple of (stiffness, damping) tensors
        """
        # Scale impedance to [0, impedance_scale]
        impedance_values = (impedance_actions + 1.0) / 2.0 * self.action_impedance_scale
        
        # Convert impedance to stiffness and damping
        # High impedance -> High stiffness, Low damping
        # Low impedance -> Low stiffness, High damping
        
        # Stiffness: directly proportional to impedance
        stiffness = impedance_values
        
        # Damping: inversely related to impedance, with minimum and maximum bounds
        min_damping = 1.0   # Minimum damping for stability
        max_damping = 20.0  # Maximum damping to prevent sluggish response
        
        # Normalize impedance to [0, 1] for damping calculation
        normalized_impedance = impedance_values / self.action_impedance_scale
        
        # Damping decreases as impedance increases
        damping = max_damping - normalized_impedance * (max_damping - min_damping)
        
        # Ensure values are within safe bounds
        stiffness = torch.clamp(stiffness, min=0.1, max=self.action_impedance_scale)
        damping = torch.clamp(damping, min=min_damping, max=max_damping)
        
        # Debug logging (if enabled)
        if self.cfg.impedance_debug:
            env_0_impedance = impedance_values[0, 0].item()
            env_0_stiffness = stiffness[0, 0].item()
            env_0_damping = damping[0, 0].item()
            print(f"[DEBUG] Impedance conversion (env 0, joint 0): impedance={env_0_impedance:.3f} -> stiffness={env_0_stiffness:.3f}, damping={env_0_damping:.3f}")
        
        return stiffness, damping

    def _apply_action(self) -> None:
        """Apply actions to the articulation."""
        num_joints = self.inspire_hand.num_joints

        # Split actions: pos_delta(12) + impedance(12) = 24D total
        actions_joint_pos_delta = self.actions_buf[:, :num_joints]
        actions_impedance = self.actions_buf[:, num_joints:2*num_joints]

        # Scale position delta actions
        # Pos Delta: current_pos + delta * scale
        target_joint_pos = self.inspire_hand.data.joint_pos + actions_joint_pos_delta * self.action_joint_pos_scale
        
        # Apply joint coupling
        target_joint_pos = self._apply_joint_coupling(target_joint_pos)
        
        # Convert impedance to stiffness and damping
        target_stiffness, target_damping = self._impedance_to_stiffness_damping(actions_impedance)

        # Apply actions
        self.inspire_hand.set_joint_position_target(target_joint_pos)
        self.inspire_hand.write_joint_stiffness_to_sim(target_stiffness)
        self.inspire_hand.write_joint_damping_to_sim(target_damping)


    def _get_observations(self) -> dict:
        """Compute observations for the RL agent."""
        # Compute the base observation
        base_obs = self._compute_base_observations()

        # --- Frame Stacking --- #
        stacked_obs_list = []
        for env_idx in range(self.num_envs):
            obs_to_store = base_obs[env_idx].clone().detach()

            if len(self._obs_history[env_idx]) == 0:
                # Fill deque if empty (first step after reset)
                for _ in range(self.cfg.num_frames_to_stack):
                    self._obs_history[env_idx].append(obs_to_store)
            else:
                # Append current observation
                self._obs_history[env_idx].append(obs_to_store)

            # Stack observations in the deque along a new dimension
            stacked_env_tensor = torch.cat(list(self._obs_history[env_idx]), dim=0)
            stacked_obs_list.append(stacked_env_tensor)

        final_stacked_obs = torch.stack(stacked_obs_list, dim=0)
        return {"policy": final_stacked_obs}


    def _get_rewards(self) -> torch.Tensor:
        """Compute rewards for the RL agent."""

        # --- Calculate Personalized Force Error Reward --- #
        force_error_reward = torch.zeros(self.num_envs, device=self.device)
        current_forces_magnitudes = []
        debug_forces_env0 = [] # Initialize list to store debug strings for env 0
        individual_force_errors = [] # Store individual errors for aggregation

        # Use the correct sensor access method
        contact_sensor_names = [
            "contact_R_thumb_distal",
            "contact_R_index_distal",
            "contact_R_middle_distal",
            "contact_R_ring_distal",
            "contact_R_pinky_distal",
        ]
        
        for i, sensor_name in enumerate(contact_sensor_names):
            # Get personalized target force and weight for this finger
            target_force = self.cfg.finger_target_forces[i] if i < len(self.cfg.finger_target_forces) else 5.0
            weight = self.cfg.finger_force_weights[i] if i < len(self.cfg.finger_force_weights) else 1.0
            
            try:
                sensor = getattr(self, sensor_name)
                if isinstance(sensor, ContactSensor):
                    # Get current force vector (most recent reading)
                    current_force_w = sensor.data.net_forces_w[:, -1, :]
                    # Calculate the magnitude of the force
                    current_force_mag = torch.norm(current_force_w, dim=1)

                    # Append magnitude for reward calculation
                    current_forces_magnitudes.append(current_force_mag)
                    
                    # Calculate weighted squared error for this finger
                    force_error = torch.square(current_force_mag - target_force) * weight
                    individual_force_errors.append(force_error)

                    # Append formatted force string for debugging env 0
                    if self.num_envs > 0:
                        finger_name = sensor_name.split('_')[-2] # Extract finger name (e.g., thumb, index)
                        debug_forces_env0.append(f"{finger_name}: {current_force_mag[0].item():.3f}/{target_force:.1f}")

                else:
                    print(f"[Warning] _get_rewards: Attribute '{sensor_name}' is not a ContactSensor.")
                    # Handle missing/wrong sensor: Append zero force for reward and debug
                    zero_force_mag = torch.zeros(self.num_envs, device=self.device)
                    current_forces_magnitudes.append(zero_force_mag)
                    force_error = torch.square(target_force) * weight
                    individual_force_errors.append(force_error)
                    if self.num_envs > 0:
                        finger_name = sensor_name.split('_')[-2]
                        debug_forces_env0.append(f"{finger_name}: 0.000/{target_force:.1f} (Error)")

            except AttributeError:
                print(f"[Warning] _get_rewards: Sensor attribute '{sensor_name}' not found.")
                # Handle missing sensor attribute: Append zero force for reward and debug
                zero_force_mag = torch.zeros(self.num_envs, device=self.device)
                current_forces_magnitudes.append(zero_force_mag)
                force_error = torch.square(target_force) * weight
                individual_force_errors.append(force_error)
                if self.num_envs > 0:
                    finger_name = sensor_name.split('_')[-2]
                    debug_forces_env0.append(f"{finger_name}: 0.000/{target_force:.1f} (Missing)")

        # Aggregate individual force errors based on configuration
        force_error_reward = self._aggregate_force_errors(individual_force_errors)
        
        # Log force data to TensorBoard
        self._log_force_data_to_tensorboard(current_forces_magnitudes, individual_force_errors, self.common_step_counter)

        # Print collected forces for env 0 at every step
        if self.num_envs > 0:
            print(f"Step {self.common_step_counter} Forces Env 0: | {' | '.join(debug_forces_env0)} |")

        # Apply scaling factor (negative since it's a penalty)
        force_error_reward *= self.cfg.rew_force_error_scale

        # --- Calculate Grasping Reward Components --- #
        # Object-to-palm distance reward
        object_distance_reward = self._compute_object_distance_reward()
        
        # Multi-finger contact reward
        multi_contact_reward = self._compute_multi_contact_reward(current_forces_magnitudes)
        
        # Object velocity stability reward
        object_stability_reward = self._compute_object_stability_reward()
        
        # Finger distribution around object reward
        finger_distribution_reward = self._compute_finger_distribution_reward()
        
        # Grasp bonus reward
        grasp_bonus_reward = self._compute_grasp_bonus_reward(current_forces_magnitudes)

        # --- Calculate Action Rate Penalty --- #
        action_rate_penalty = torch.sum(torch.square(self.actions_buf - self.last_actions), dim=1)
        action_rate_penalty *= self.cfg.rew_action_rate_scale
        self.last_actions = self.actions_buf.clone()

        # --- Calculate Joint Velocity Penalty --- #
        joint_vel_penalty = torch.sum(torch.square(self.inspire_hand.data.joint_vel), dim=1)
        joint_vel_penalty *= self.cfg.rew_joint_vel_scale

        # --- Alive Reward --- #
        alive_reward = torch.ones(self.num_envs, device=self.device) * self.cfg.rew_alive_scale

        # --- Termination Penalty (applied implicitly by agent if reset_terminated is used) --- #
        # We can add an explicit penalty here if desired, based on self.reset_terminated
        # termination_penalty = self.reset_terminated.float() * self.cfg.rew_terminate_scale

        # --- Total Reward --- #
        total_reward = (
            force_error_reward
            + object_distance_reward
            + multi_contact_reward
            + object_stability_reward
            + finger_distribution_reward
            + grasp_bonus_reward
            + action_rate_penalty
            + joint_vel_penalty
            + alive_reward
            # + termination_penalty # Optional explicit penalty
        )

        # Debug: Print reward components occasionally
        if self.common_step_counter % 200 == 0:
            print(f"Step {self.common_step_counter} Reward Components (mean):")
            print(f"  Force Error: {torch.mean(force_error_reward).item():.3f}")
            print(f"  Object Distance: {torch.mean(object_distance_reward).item():.3f}")
            print(f"  Multi Contact: {torch.mean(multi_contact_reward).item():.3f}")
            print(f"  Object Stability: {torch.mean(object_stability_reward).item():.3f}")
            print(f"  Finger Distribution: {torch.mean(finger_distribution_reward).item():.3f}")
            print(f"  Grasp Bonus: {torch.mean(grasp_bonus_reward).item():.3f}")
            print(f"  Action Rate: {torch.mean(action_rate_penalty).item():.3f}")
            print(f"  Joint Vel: {torch.mean(joint_vel_penalty).item():.3f}")
            print(f"  Alive: {torch.mean(alive_reward).item():.3f}")
            print(f"  Total: {torch.mean(total_reward).item():.3f}")
            print(f"  Force Magnitudes: {[torch.mean(f).item() for f in current_forces_magnitudes]}")

        return total_reward

    def _compute_object_distance_reward(self) -> torch.Tensor:
        """Compute reward based on object proximity to palm center."""
        # Get object position
        object_pos = self.sphere.data.root_pos_w
        
        # Get hand palm center (approximate using hand root position)
        hand_root_pos = self.inspire_hand.data.root_pos_w
        
        # Calculate distance between object and hand palm
        distance = torch.norm(object_pos - hand_root_pos, dim=1)
        
        # Convert to reward (negative distance, scaled)
        distance_reward = -distance * self.cfg.rew_object_distance_scale
        
        return distance_reward

    def _compute_multi_contact_reward(self, current_forces_magnitudes: list[torch.Tensor]) -> torch.Tensor:
        """Compute reward for multiple finger contacts."""
        # Count fingers with meaningful contact (force > threshold)
        contact_threshold = 0.5  # Minimum force to consider as contact
        contact_count = torch.zeros(self.num_envs, device=self.device)
        
        for force_mag in current_forces_magnitudes:
            is_contact = (force_mag > contact_threshold).float()
            contact_count += is_contact
        
        # Reward increases with more contacts, but with diminishing returns
        # Use square root to provide diminishing returns
        multi_contact_reward = torch.sqrt(contact_count) * self.cfg.rew_multi_contact_scale
        
        return multi_contact_reward

    def _compute_object_stability_reward(self) -> torch.Tensor:
        """Compute reward for object velocity stability."""
        # Get object linear and angular velocities
        object_lin_vel = self.sphere.data.root_lin_vel_w
        object_ang_vel = self.sphere.data.root_ang_vel_w
        
        # Calculate velocity magnitudes
        lin_vel_magnitude = torch.norm(object_lin_vel, dim=1)
        ang_vel_magnitude = torch.norm(object_ang_vel, dim=1)
        
        # Total velocity magnitude
        total_vel_magnitude = lin_vel_magnitude + ang_vel_magnitude
        
        # Reward stability (lower velocity is better)
        stability_reward = -total_vel_magnitude * self.cfg.rew_object_stability_scale
        
        return stability_reward

    def _compute_finger_distribution_reward(self) -> torch.Tensor:
        """Compute reward for finger distribution around object."""
        # Get object position
        object_pos = self.sphere.data.root_pos_w
        
        # Get fingertip positions (approximate using hand joints)
        # For simplicity, use hand root position as reference
        hand_root_pos = self.inspire_hand.data.root_pos_w
        
        # Calculate spread of fingers around object
        # For now, use a simple approximation based on hand configuration
        # This could be improved with actual fingertip positions
        
        # Use joint positions to estimate finger spread
        joint_pos = self.inspire_hand.data.joint_pos
        joint_pos_std = torch.std(joint_pos, dim=1)
        
        # Reward finger diversity/spread
        distribution_reward = joint_pos_std * self.cfg.rew_finger_distribution_scale
        
        return distribution_reward

    def _compute_grasp_bonus_reward(self, current_forces_magnitudes: list[torch.Tensor]) -> torch.Tensor:
        """Compute bonus reward for successful grasp."""
        # Define grasp success criteria
        min_contact_force = 1.0  # Minimum force per finger
        min_contact_fingers = 3  # Minimum number of fingers in contact
        
        # Count fingers with sufficient contact force
        sufficient_contact_count = torch.zeros(self.num_envs, device=self.device)
        
        for force_mag in current_forces_magnitudes:
            is_sufficient_contact = (force_mag > min_contact_force).float()
            sufficient_contact_count += is_sufficient_contact
        
        # Check if grasp success criteria are met
        grasp_success = (sufficient_contact_count >= min_contact_fingers).float()
        
        # Apply bonus reward
        grasp_bonus_reward = grasp_success * self.cfg.rew_grasp_bonus_scale
        
        return grasp_bonus_reward

    def _aggregate_force_errors(self, individual_force_errors: list[torch.Tensor]) -> torch.Tensor:
        """Aggregate individual force error rewards based on the specified method."""
        if self.cfg.force_error_aggregation == "weighted_sum":
            # Sum of weighted squared errors
            return torch.sum(torch.cat(individual_force_errors, dim=1), dim=1)
        elif self.cfg.force_error_aggregation == "mean":
            # Mean of squared errors
            return torch.mean(torch.cat(individual_force_errors, dim=1), dim=1)
        elif self.cfg.force_error_aggregation == "max":
            # Maximum squared error
            return torch.max(torch.cat(individual_force_errors, dim=1), dim=1).values
        else:
            print(f"[WARNING] Unknown force_error_aggregation: {self.cfg.force_error_aggregation}. Defaulting to weighted_sum.")
            return torch.sum(torch.cat(individual_force_errors, dim=1), dim=1)


    def _get_dones(self) -> tuple[torch.Tensor, torch.Tensor]:
        """Compute dones and timeouts for the RL agent."""

        # 1. Timeouts: reset if episode length exceeds limit
        time_out = self.episode_length_buf >= self.max_episode_length

        # 2. Dropped sphere: reset if sphere is too far from hand root
        # Get sphere position
        sphere_pos_w = self.sphere.data.root_pos_w
        # Reinstate distance-based calculation
        hand_root_pos_w = self.inspire_hand.data.root_pos_w # Get hand's actual root position
        distance_sq = torch.sum(torch.square(sphere_pos_w - hand_root_pos_w), dim=1) # Calculate distance sphere <-> hand root
        sphere_dropped = distance_sq > (self.cfg.termination_drop_distance ** 2)

        # Remove Z-coordinate comparison
        # sphere_z = sphere_pos_w[:, 2]
        # hand_root_z = self.inspire_hand.data.root_pos_w[:, 2] # Get hand Z coord
        # sphere_dropped = sphere_z < hand_root_z # Sphere is dropped if its Z is lower than hand's Z

        # 3. Excessive forces: reset if any fingertip force exceeds threshold
        max_force_exceeded = torch.zeros_like(time_out)
        contact_sensor_names = [ # Re-use the list defined earlier or define again
            "contact_R_thumb_distal",
            "contact_R_index_distal",
            "contact_R_middle_distal",
            "contact_R_ring_distal",
            "contact_R_pinky_distal",
        ]
        for sensor_name in contact_sensor_names:
            try:
                sensor = getattr(self, sensor_name)
                if isinstance(sensor, ContactSensor):
                    current_force_w = sensor.data.net_forces_w[:, -1, :]
                    current_force_mag = torch.norm(current_force_w, dim=1)
                    max_force_exceeded = torch.logical_or(max_force_exceeded, current_force_mag > self.cfg.termination_force_threshold)
                else:
                    # Optional: warning if attribute exists but isn't a sensor
                    print(f"[Warning] _get_dones: Attribute '{sensor_name}' is not a ContactSensor.")
            except AttributeError:
                # Optional: warning if sensor attribute doesn't exist
                print(f"[Warning] _get_dones: Sensor attribute '{sensor_name}' not found.")
                # If a sensor is missing, max_force_exceeded remains unchanged for this sensor

        # Combine reset conditions
        reset_buf = time_out | sphere_dropped | max_force_exceeded
        # reset_buf = time_out # Keep this commented out

        # Debug: Print reset reasons occasionally
        if self.common_step_counter % 20 == 0: # Check every 20 steps
            reset_env_ids = reset_buf.nonzero(as_tuple=False).squeeze(-1)
            if len(reset_env_ids) > 0:
                first_reset_id = reset_env_ids[0].item() # Get index of the first env resetting this step
                print(f"--- Env {first_reset_id}, Step {self.common_step_counter}: Reset Triggered ---")
                if time_out[first_reset_id]:
                    print(f"  Reason: Timeout (Episode Length: {self.episode_length_buf[first_reset_id].item()})")

                # Check sphere dropped for the specific env
                if sphere_dropped[first_reset_id]:
                     # Use the distance squared value calculated above
                     dist_sq_val = distance_sq[first_reset_id].item()
                     threshold_sq = self.cfg.termination_drop_distance ** 2
                     print(f"  Reason: Sphere Dropped (Distance Sq: {dist_sq_val:.4f} > Threshold Sq: {threshold_sq:.4f})")

                # Check max_force_exceeded for the specific env
                # (Use the already computed max_force_exceeded tensor)
                if max_force_exceeded[first_reset_id]:
                     print(f"  Reason: Max Force Exceeded (Threshold: {self.cfg.termination_force_threshold})")

                print(f"------------------------------------------------")


        return reset_buf, time_out

    def _reset_idx(self, env_ids: Sequence[int] | None):
        if env_ids is None:
            env_ids = torch.arange(self.num_envs, device=self.device)
        # --> Add calculation for num_resets here <--
        num_resets = len(env_ids)

        # -- Reset Articulation --
        # Access default states directly from self.inspire_hand.data
        # print(f"[DEBUG] Attempting to access self.inspire_hand.data...")
        if self.inspire_hand is None:
            print(f"[ERROR] self.inspire_hand is None!")
        elif self.inspire_hand.data is None:
            print(f"[ERROR] self.inspire_hand.data exists but is None!")

        # Correctly access default stiffness/damping from the config (cfg), not the instance (inspire_hand)
        default_stiffness = torch.full((num_resets, self.inspire_hand.num_joints), self.cfg.inspire_hand_cfg.actuators["inspire_hand_actuators"].stiffness, device=self.device)
        default_damping = torch.full((num_resets, self.inspire_hand.num_joints), self.cfg.inspire_hand_cfg.actuators["inspire_hand_actuators"].damping, device=self.device)
        # Write state to sim
        joint_pos = self.inspire_hand.data.default_joint_pos[env_ids]
        joint_vel = self.inspire_hand.data.default_joint_vel[env_ids]
        self.inspire_hand.write_joint_state_to_sim(joint_pos, joint_vel, None, env_ids)
        # Reset drive targets using correct API
        # self.inspire_hand.set_joint_drive_properties(stiffness=default_stiffness, damping=default_damping, env_ids=env_ids)
        self.inspire_hand.write_joint_stiffness_to_sim(default_stiffness, env_ids=env_ids)
        self.inspire_hand.write_joint_damping_to_sim(default_damping, env_ids=env_ids)

        # -- Reset Rigid Object --
        obj_state = self.sphere.data.default_root_state[env_ids].clone()
        # Randomize position slightly around the default initial position
        # Default pos is defined in config/objects_cfg.py -> init_state
        # Make sure the default pos is reasonable relative to the hand's fixed base
        # pos_offset = sample_uniform(-0.02, 0.02, (num_resets, 3), device=self.device)
        # obj_state[:, :3] += pos_offset
        # Add environment origin offset
        obj_state[:, :3] += self.scene.env_origins[env_ids]
        # Set velocity to zero
        obj_state[:, 7:] = 0.0
        self.sphere.write_root_state_to_sim(obj_state, env_ids)
        # <<< Reset hand root state >>>
        if self.inspire_hand is not None and self.inspire_hand.data is not None:
            hand_root_state = self.inspire_hand.data.default_root_state[env_ids].clone()
            hand_root_state[:, :3] += self.scene.env_origins[env_ids]
            hand_root_state[:, 7:] = 0.0 # Zero linear and angular velocity
            self.inspire_hand.write_root_state_to_sim(hand_root_state, env_ids)
        else:
            print(f"[ERROR] _reset_idx: Cannot reset hand root state because inspire_hand or its data is None.")
        # <<< End reset hand root state >>>
        # -- Reset Buffers --
        self.actions_buf[env_ids] = 0
        self.last_actions[env_ids] = 0

        # -- Reset Observation History --
        # Compute initial observations for the reset envs BEFORE clearing history
        initial_base_obs = self._compute_base_observations()[env_ids]
        for i, env_idx in enumerate(env_ids):
            self._obs_history[env_idx].clear()
            obs_to_fill = initial_base_obs[i].clone().detach()
            for _ in range(self.cfg.num_frames_to_stack):
                self._obs_history[env_idx].append(obs_to_fill)

        # Call parent reset last
        super()._reset_idx(env_ids)


    def _compute_base_observations(self) -> torch.Tensor:
        """Helper function to compute the base observation part."""
        # Hand joint state - access directly from self.inspire_hand.data
        joint_pos = self.inspire_hand.data.joint_pos
        joint_vel = self.inspire_hand.data.joint_vel
        joint_stiffness = self.inspire_hand.data.joint_stiffness
        joint_damping = self.inspire_hand.data.joint_damping

        # Sphere pose (relative to env origin, converted to 6D rot)
        sphere_pos_w = self.sphere.data.root_pos_w
        sphere_rot_w = self.sphere.data.root_quat_w
        sphere_pos_env = sphere_pos_w - self.scene.env_origins
        sphere_rot_6d_env = quat_to_rot_6d_pt(sphere_rot_w)

        # Contact forces
        contact_sensor_names = [
            "contact_R_thumb_distal",
            "contact_R_index_distal",
            "contact_R_middle_distal",
            "contact_R_ring_distal",
            "contact_R_pinky_distal",
        ]
        contact_forces = []
        for sensor_name in contact_sensor_names:
            try:
                # Access sensor object as a direct attribute of the env instance
                sensor = getattr(self, sensor_name)
                if isinstance(sensor, ContactSensor):
                     force_w = sensor.data.net_forces_w[:, -1, :] # Get the latest force reading
                     contact_forces.append(force_w)
                else:
                     # This case should ideally not happen if config is correct
                     print(f"[Warning] Attribute '{sensor_name}' exists but is not a ContactSensor. Type: {type(sensor)}. Appending zeros.")
                     zeros_force = torch.zeros((self.num_envs, 3), device=self.device, dtype=torch.float32)
                     contact_forces.append(zeros_force)
            except AttributeError:
                 # Handle cases where the sensor attribute doesn't exist on self
                 print(f"[Error] Sensor attribute '{sensor_name}' not found on environment instance. Check config and initialization. Appending zeros.")
                 zeros_force = torch.zeros((self.num_envs, 3), device=self.device, dtype=torch.float32)
                 contact_forces.append(zeros_force)

        all_contact_forces = torch.cat(contact_forces, dim=1) # Shape: (num_envs, 5 * 3)

        # Concatenate all parts
        base_obs = torch.cat(
            [
                joint_pos,
                joint_vel,
                joint_stiffness,
                joint_damping,
                sphere_pos_env,
                sphere_rot_6d_env,
                all_contact_forces,
            ],
            dim=1,
        )
        return base_obs

# Example usage with all enhanced features enabled:
"""
# In your training script, you can customize the environment like this:

from envs.inspire_hand_force_env import InspireHandForceEnvCfg

# Create custom configuration with enhanced features
custom_cfg = InspireHandForceEnvCfg()

# Joint coupling configuration
custom_cfg.finger_coupling_ratio = 0.8  # 80% coupling between MCP and DIP
custom_cfg.thumb_coupling_enabled = True
custom_cfg.coupling_debug = False  # Set to True for debugging

# Hybrid action space (24D: pos_delta + impedance)
# No changes needed - automatically uses 24D action space

# Personalized force rewards
custom_cfg.finger_target_forces = [6.0, 5.0, 4.5, 3.5, 2.5]  # Different targets per finger
custom_cfg.finger_force_weights = [1.2, 1.0, 1.0, 0.8, 0.6]  # Different weights per finger
custom_cfg.force_error_aggregation = "weighted_sum"  # or "mean", "max"

# Multi-stage grasping rewards
custom_cfg.rew_object_distance_scale = 3.0    # Encourage object proximity
custom_cfg.rew_multi_contact_scale = 1.5      # Reward multiple contacts
custom_cfg.rew_object_stability_scale = 0.8   # Reward object stability
custom_cfg.rew_finger_distribution_scale = 0.5 # Reward finger spread
custom_cfg.rew_grasp_bonus_scale = 10.0       # Big bonus for successful grasp

# TensorBoard logging
custom_cfg.tb_log_freq = 50                   # Log every 50 steps
custom_cfg.tb_log_forces = True               # Enable force logging
custom_cfg.tb_log_detailed = True             # Enable detailed statistics

# In your training script:
import gym
from stable_baselines3 import SAC

env = gym.make("Isaac-InspireHandForce-Direct-v0", cfg=custom_cfg)
agent = SAC("MlpPolicy", env, tensorboard_log="./tb_logs/")

# Enable TensorBoard logging for forces
env.set_tensorboard_logger(agent.logger)

# Train with enhanced features
agent.learn(total_timesteps=1_000_000)
"""

# Register the environment with gym
import gymnasium as gym
gym.register(
    id="Isaac-InspireHandForce-Direct-v0",
    entry_point="envs.inspire_hand_force_env:InspireHandForceEnv",
    kwargs={"cfg": InspireHandForceEnvCfg}
) 