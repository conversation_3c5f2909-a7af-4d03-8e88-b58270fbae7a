# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""脚本用于使用Stable Baselines3训练Tora机器人。

由于Stable-Baselines3不直接支持GPU上的缓冲区，
我们建议使用较少数量的环境。否则，
在GPU->CPU传输中会有显著的开销。
"""

"""首先启动Isaac Sim模拟器。"""

import argparse
import sys

from isaaclab.app import AppLauncher

# 添加命令行参数
parser = argparse.ArgumentParser(description="使用Stable-Baselines3训练Tora机器人。")
parser.add_argument(
    "--video", action="store_true", default=False, help="在训练期间录制视频。"
)
parser.add_argument(
    "--video_length", type=int, default=200, help="录制视频的长度（步数）。"
)
parser.add_argument(
    "--video_interval", type=int, default=2000, help="录制视频的间隔（步数）。"
)
parser.add_argument("--num_envs", type=int, default=None, help="要模拟的环境数量。")
parser.add_argument(
    "--task", type=str, default="Isaac-TORA-Direct-v0", help="任务名称。"
)
parser.add_argument("--seed", type=int, default=None, help="环境使用的种子")
parser.add_argument(
    "--max_iterations", type=int, default=None, help="RL策略训练迭代次数。"
)
parser.add_argument(
    "--load_model", type=str, default=None, help="要继续训练的已保存模型的路径"
)
# 添加AppLauncher命令行参数
AppLauncher.add_app_launcher_args(parser)
# 解析参数
args_cli, hydra_args = parser.parse_known_args()
# 如果启用视频录制，总是启用相机
if args_cli.video:
    args_cli.enable_cameras = True

# 为Hydra清理sys.argv
sys.argv = [sys.argv[0]] + hydra_args

# 启动omniverse应用
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""然后执行其余部分。"""

import gymnasium as gym
import numpy as np
import os
import random
from datetime import datetime

from stable_baselines3 import SAC
from stable_baselines3.common.callbacks import CheckpointCallback
from stable_baselines3.common.logger import configure
from stable_baselines3.common.vec_env import VecNormalize

from isaaclab.envs import (
    DirectRLEnvCfg,
)
from isaaclab.utils.dict import print_dict
from isaaclab.utils.io import dump_pickle, dump_yaml

from isaaclab_rl.sb3 import Sb3VecEnvWrapper, process_sb3_cfg

# 确保导入我们的环境
import sys
import os

# 将包含IsaacSim_direct_task_table_set_env.py的目录添加到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 导入我们的环境
import IsaacSim_direct_task_table_set_env
from IsaacSim_direct_task_table_set_env import ToraDirectEnvCfg


def main():
    """使用stable-baselines3代理进行训练。"""
    # 如果seed = -1，随机采样种子
    if args_cli.seed == -1:
        args_cli.seed = random.randint(0, 10000)

    # 使用默认配置
    env_cfg = ToraDirectEnvCfg()

    # 创建默认的agent配置
    agent_cfg = {
        "policy": "MlpPolicy",
        # SAC特有参数
        "buffer_size": 1000000,  # 回放缓冲区大小
        "learning_starts": 1000,  # 开始学习前收集的样本数
        "batch_size": 256,  # 更大的批量大小通常对SAC有利
        "tau": 0.005,  # 软更新系数
        "gamma": 0.99,  # 折扣因子
        "train_freq": 1,  # 每步更新一次
        "gradient_steps": 1,  # 每步执行一次梯度更新
        "ent_coef": "auto",  # 自动调整熵系数
        "target_update_interval": 1,  # 每步更新目标网络
        "learning_rate": 3e-4,  # 学习率
        "use_sde": False,  # 是否使用gSDE探索
        "use_sde_at_warmup": False,
        # 通用参数
        "tensorboard_log": "logs/sac",
        "verbose": 1,
        "seed": 42,
        "device": "auto",  # 自动选择设备
        # 训练长度
        "n_timesteps": 1000000,  # 总训练步数
    }

    # 使用命令行参数覆盖配置
    env_cfg.scene.num_envs = (
        args_cli.num_envs if args_cli.num_envs is not None else env_cfg.scene.num_envs
    )
    agent_cfg["seed"] = (
        args_cli.seed if args_cli.seed is not None else agent_cfg["seed"]
    )
    # 训练的最大迭代次数
    if args_cli.max_iterations is not None:
        agent_cfg["n_timesteps"] = (
            args_cli.max_iterations * agent_cfg["n_steps"] * env_cfg.scene.num_envs
        )

    # 设置环境种子
    # 注意：某些随机化在环境初始化时发生，所以我们在这里设置种子
    env_cfg.seed = agent_cfg["seed"]
    env_cfg.sim.device = (
        args_cli.device if args_cli.device is not None else env_cfg.sim.device
    )

    # 日志记录目录
    run_info = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    log_root_path = os.path.abspath(os.path.join("logs", "sb3", args_cli.task))
    print(f"[INFO] 在目录中记录实验: {log_root_path}")
    print(f"从命令行请求的确切实验名称: {run_info}")
    log_dir = os.path.join(log_root_path, run_info)
    os.makedirs(os.path.join(log_dir, "params"), exist_ok=True)

    # 将配置转储到日志目录中
    dump_yaml(os.path.join(log_dir, "params", "env.yaml"), env_cfg)
    dump_yaml(os.path.join(log_dir, "params", "agent.yaml"), agent_cfg)
    dump_pickle(os.path.join(log_dir, "params", "env.pkl"), env_cfg)
    dump_pickle(os.path.join(log_dir, "params", "agent.pkl"), agent_cfg)

    # 后处理代理配置
    processed_agent_cfg = process_sb3_cfg(agent_cfg)
    if "verbose" in processed_agent_cfg:
        processed_agent_cfg.pop("verbose")

    # 读取关于代理训练的配置
    policy_arch = processed_agent_cfg.pop("policy")
    n_timesteps = processed_agent_cfg.pop("n_timesteps")

    # 创建isaac环境
    env = gym.make(
        args_cli.task, cfg=env_cfg, render_mode="rgb_array" if args_cli.video else None
    )

    # 视频录制包装
    if args_cli.video:
        os.makedirs(os.path.join(log_dir, "videos", "train"), exist_ok=True)
        video_kwargs = {
            "video_folder": os.path.join(log_dir, "videos", "train"),
            "step_trigger": lambda step: step % args_cli.video_interval == 0,
            "video_length": args_cli.video_length,
            "disable_logger": True,
        }
        print("[INFO] 在训练期间录制视频。")
        print_dict(video_kwargs, nesting=4)
        env = gym.wrappers.RecordVideo(env, **video_kwargs)

    # 为stable baselines包装环境
    env = Sb3VecEnvWrapper(env)

    # 首先检查是否需要加载已有模型
    if args_cli.load_model is not None:
        print(f"[INFO] 加载模型: {args_cli.load_model}")

        # 先检查是否有保存的标准化状态
        vec_normalize_path = os.path.dirname(args_cli.load_model) + "/vec_normalize.pkl"
        if os.path.exists(vec_normalize_path):
            print(f"[INFO] 加载标准化状态: {vec_normalize_path}")
            env = VecNormalize.load(vec_normalize_path, env)
        elif "normalize_input" in processed_agent_cfg:
            # 如果没有保存的状态但需要标准化，创建新的标准化器
            print("[INFO] 未找到已保存的标准化状态，创建新的标准化器")
            env = VecNormalize(
                env,
                training=True,
                norm_obs=processed_agent_cfg.pop("normalize_input"),
                norm_reward=processed_agent_cfg.pop("normalize_value", True),
                clip_obs=processed_agent_cfg.pop("clip_obs", 10.0),
                gamma=processed_agent_cfg["gamma"],
                clip_reward=np.inf,
            )

        # 加载模型
        agent = SAC.load(args_cli.load_model, env=env)

        # 可选：更新某些参数
        # agent.learning_rate = processed_agent_cfg.get("learning_rate", 3e-4)
        # agent.batch_size = processed_agent_cfg.get("batch_size", 256)
    else:
        # 如果不加载模型，按需创建标准化器
        if "normalize_input" in processed_agent_cfg:
            env = VecNormalize(
                env,
                training=True,
                norm_obs=processed_agent_cfg.pop("normalize_input"),
                norm_reward=processed_agent_cfg.pop("normalize_value", True),
                clip_obs=processed_agent_cfg.pop("clip_obs", 10.0),
                gamma=processed_agent_cfg["gamma"],
                clip_reward=np.inf,
            )

        # 创建新模型
        agent = SAC(policy_arch, env, verbose=1, **processed_agent_cfg)
    # 配置记录器
    new_logger = configure(log_dir, ["stdout", "tensorboard"])
    agent.set_logger(new_logger)
    # 代理的回调
    checkpoint_callback = CheckpointCallback(
        save_freq=1000, save_path=log_dir, name_prefix="model", verbose=2
    )
    # 训练代理
    if args_cli.load_model is not None:
        # 继续训练时，不重置步数
        agent.learn(
            total_timesteps=n_timesteps,
            callback=checkpoint_callback,
            reset_num_timesteps=False,
        )
    else:
        agent.learn(total_timesteps=n_timesteps, callback=checkpoint_callback)
    # 保存最终模型
    agent.save(os.path.join(log_dir, "model"))

    # 关闭模拟器
    env.close()


if __name__ == "__main__":
    # 运行主函数
    main()
    # 关闭sim应用
    simulation_app.close()
