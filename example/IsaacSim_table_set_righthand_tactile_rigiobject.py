"""Isaac<PERSON><PERSON>_table_set_righthand_tactile_rigiobject.py

A script to simulate righthand with grasp rigiobject actions.

@Author: <PERSON><PERSON><PERSON><PERSON>
@Date: 2025-03-14
"""

import argparse
import torch

from isaaclab.app import AppLauncher


# add argparse arguments
parser = argparse.ArgumentParser(description="Tutorial on adding sensors on a robot.")
parser.add_argument(
    "--num_envs", type=int, default=2, help="Number of environments to spawn."
)

# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import isaaclab.sim as sim_utils

##
# Pre-defined configs
##
from px_janus_learnsim.robot.tora.tora import TORA_CFG
import numpy as np

from isaaclab.sensors import CameraCfg, ContactSensor, ContactSensorCfg
from isaaclab.assets import ArticulationCfg, AssetBaseCfg, RigidObjectCfg
from isaaclab.scene import InteractiveScene, InteractiveSceneCfg
from isaaclab.utils import configclass
from isaaclab.managers import SceneEntityCfg
from isaaclab.markers import VisualizationMarkers
from isaaclab.markers.config import FRAME_MARKER_CFG
from px_janus_learnsim.config.paths import ROBOT_MODELS_PATH, ASSET_PATH


from scipy.spatial.transform import Rotation as R
from isaaclab.sensors import ContactSensorCfg
from px_janus_learnsim.robot.DexH13.DexH13_right_with_contact_sensor import (
    DexH13_right_CFG,
)

import sharklog
from tlpy.interfaces.robot import ToraRHandJointName, ToraJoints
from tlpy.drivers.isaac_tora_driver import IsaacBadRHandDriver

from isaaclab.utils.assets import ISAAC_NUCLEUS_DIR
from isaaclab.actuators import ImplicitActuatorCfg


@configclass
class TableSceneCfg(InteractiveSceneCfg):
    """Scene configuration with robot, table, and objects."""

    @configclass
    class RobotInitialState(AssetBaseCfg.InitialStateCfg):
        """Extended initial state configuration for the robot."""

        pos: tuple[float, float, float] = (0.0, 0.5, 0.0)
        rot: tuple[float, float, float, float] = (1.0, 0.0, 0.0, 0.0)
        lin_vel: tuple[float, float, float] = (0.0, 0.0, 0.0)
        ang_vel: tuple[float, float, float] = (0.0, 0.0, 0.0)

    # Ground plane
    ground = AssetBaseCfg(
        prim_path="/World/defaultGroundPlane", spawn=sim_utils.GroundPlaneCfg()
    )
    # lights
    dome_light = AssetBaseCfg(
        prim_path="/World/Light",
        spawn=sim_utils.DomeLightCfg(intensity=3000.0, color=(0.75, 0.75, 0.75)),
    )
    # Robot with adjusted origin
    # articulation
    right_hand: ArticulationCfg = DexH13_right_CFG.replace(
        prim_path="{ENV_REGEX_NS}/Robot",
        init_state=ArticulationCfg.InitialStateCfg(
            pos=(-0.5, 0.1, 1.18),  # 调整初始位置
            rot=(0.0, 0.0, -0.7071, 0.7071),
            lin_vel=(0.0, 0.0, 0.0),
            ang_vel=(0.0, 0.0, 0.0),
            joint_pos={
                # 拇指关节
                "mzcb": 0.00,  # 拇指侧摆
                "mzjdxz": 0.00,  # 拇指近端旋转
                "mzzd": 0.00,  # 拇指远端旋转
                "mzydxz": 0.00,  # 拇指掌端旋转
                # 食指关节
                "szcb": 0.0,  # 食指侧摆
                "szjdxz": 0.0,  # 食指近端旋转
                "szzdxz": 0.0,  # 食指远端旋转
                "szydxz": 0.0,  # 食指掌端旋转
                # 中指关节
                "zzcb": 0.0,  # 中指侧摆
                "zzjdxz": 0.0,  # 中指近端旋转
                "zzzdxz": 0.0,  # 中指远端旋转
                "zzydxz": 0.0,  # 中指掌端旋转
                # 无名指关节
                "wmzcb": 0.0,  # 无名指侧摆
                "wmzjdxz": 0.0,  # 无名指近端旋转
                "wmzzdxz": 0.0,  # 无名指远端旋转
                "wmzydxz": 0.0,  # 无名指掌端旋转
            },
            joint_vel={".*": 0.0},  # 所有关节初始速度为0
        ),
    )

    table = AssetBaseCfg(
        prim_path="{ENV_REGEX_NS}/table",
        spawn=sim_utils.UsdFileCfg(
            usd_path=f"{ASSET_PATH}/table_2.usdc",
            scale=np.array([0.9, 0.9, 0.8]),
        ),
        init_state=AssetBaseCfg.InitialStateCfg(
            pos=(0.0, -0.4, 0.0),  # 相对于机器人的位置
            rot=(0.7071, 0.7071, 0.0, 0.0),
        ),
    )

    # # Objects
    # bottle = RigidObjectCfg(
    #     prim_path="{ENV_REGEX_NS}/bottle",
    #     spawn=sim_utils.UsdFileCfg(
    #         usd_path=f"{ASSET_PATH}/ImageToStl.com_obj_000030.usd",
    #         scale=np.array([0.001, 0.001, 0.001]),
    #         mass_props=sim_utils.MassPropertiesCfg(mass=0.5),
    #         collision_props=sim_utils.CollisionPropertiesCfg(
    #             collision_enabled=True,
    #         ),
    #         # physics_material=sim_utils.RigidBodyMaterialCfg(
    #         #     static_friction=1,  # 静摩擦系数
    #         #     dynamic_friction=1,  # 动摩擦系数
    #         #     restitution=0.1,  # 弹性恢复系数，接近 1 表示更弹性
    #         # ),
    #     ),
    #     init_state=RigidObjectCfg.InitialStateCfg(
    #         pos=(-0.2, -0.25, 1.0743),
    #         rot=(0.707, 0.707, 0.0, 0.0),  # 90度绕X轴旋转
    #     ),
    # )

    bottle = RigidObjectCfg(
        prim_path="{ENV_REGEX_NS}/Cube0",
        spawn=sim_utils.CuboidCfg(
            # size=(1, 1, 1.5),
            size=(0.05, 0.05, 0.11),
            rigid_props=sim_utils.RigidBodyPropertiesCfg(),
            mass_props=sim_utils.MassPropertiesCfg(mass=0.5),
            collision_props=sim_utils.CollisionPropertiesCfg(
                collision_enabled=True,
            ),
            physics_material=sim_utils.RigidBodyMaterialCfg(
                static_friction=1,  # 静摩擦系数
                dynamic_friction=1,  # 动摩擦系数
                restitution=0.1,  # 弹性恢复系数，接近 1 表示更弹性
            ),
            activate_contact_sensors=False,
            visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.2, 0.5, 0.9)),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(pos=(-0.2, -0.25, 1.0743)),
    )

    # Objects
    # bottle = ArticulationCfg(
    #     prim_path="{ENV_REGEX_NS}/paxini_cup",
    #     spawn=sim_utils.UsdFileCfg(
    #         usd_path=f"{ASSET_PATH}/urdf_paxinizhibei_with_collision.usd",
    #         scale=np.array([0.001, 0.001, 0.001]),
    #         rigid_props=sim_utils.RigidBodyPropertiesCfg(
    #             disable_gravity=False,
    #             # rigid_body_enabled = True,
    #             # kinematic_enabled = True,
    #             retain_accelerations=False,
    #             linear_damping=1000.0,
    #             angular_damping=1000.0,
    #             max_linear_velocity=1000.0,
    #             max_angular_velocity=1000.0,
    #             max_depenetration_velocity=1.0,
    #             # visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(1,0,0)),
    #         ),
    #         # visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(1,0,0)),
    #         articulation_props=sim_utils.ArticulationRootPropertiesCfg(
    #             enabled_self_collisions=False,
    #             solver_position_iteration_count=2,
    #             solver_velocity_iteration_count=2,
    #             sleep_threshold=0.000,
    #             stabilization_threshold=0.0000,
    #         ),
    #         # activate_contact_sensors=True,
    #     ),
    #     init_state=ArticulationCfg.InitialStateCfg(
    #         pos=(-0.285, -0.45, 1.090),
    #         rot=(0, 0, 0.0, 0.0),
    #     ),
    #     soft_joint_pos_limit_factor=0.9,
    #     actuators={
    #         ".*": ImplicitActuatorCfg(
    #             joint_names_expr=[".*"],
    #             effort_limit=2000,
    #             velocity_limit=100.0,
    #             stiffness=4000.0,
    #             damping=500.0,
    #             armature=0.01,
    #         )
    #     },
    # )

    contact_forces_szjd = ContactSensorCfg(
        prim_path="{ENV_REGEX_NS}/Robot/szjd",
        update_period=0.0,
        history_length=6,
        debug_vis=True,
        filter_prim_paths_expr=["{ENV_REGEX_NS}/Cube0"],
    )

    contact_forces_szzd = ContactSensorCfg(
        prim_path="{ENV_REGEX_NS}/Robot/szzd",
        update_period=0.0,
        history_length=6,
        debug_vis=True,
        filter_prim_paths_expr=["{ENV_REGEX_NS}/Cube0"],
    )

    contact_forces_szyd = ContactSensorCfg(
        prim_path="{ENV_REGEX_NS}/Robot/szyd",
        update_period=0.0,
        history_length=6,
        debug_vis=True,
        # filter_prim_paths_expr=["{ENV_REGEX_NS}/Cube0"],
    )

    contact_forces_zzjd = ContactSensorCfg(
        prim_path="{ENV_REGEX_NS}/Robot/zzjd",
        update_period=0.0,
        history_length=6,
        debug_vis=True,
        filter_prim_paths_expr=["{ENV_REGEX_NS}/Cube0"],
    )

    contact_forces_zzzd = ContactSensorCfg(
        prim_path="{ENV_REGEX_NS}/Robot/zzzd",
        update_period=0.0,
        history_length=6,
        debug_vis=True,
        filter_prim_paths_expr=["{ENV_REGEX_NS}/Cube0"],
    )

    contact_forces_zzyd = ContactSensorCfg(
        prim_path="{ENV_REGEX_NS}/Robot/zzyd",
        update_period=0.0,
        history_length=6,
        debug_vis=True,
        filter_prim_paths_expr=["{ENV_REGEX_NS}/Cube0"],
    )

    contact_forces_mzzd = ContactSensorCfg(
        prim_path="{ENV_REGEX_NS}/Robot/mzzd",
        update_period=0.0,
        history_length=6,
        debug_vis=True,
        filter_prim_paths_expr=["{ENV_REGEX_NS}/Cube0"],
    )

    contact_forces_mzyd = ContactSensorCfg(
        prim_path="{ENV_REGEX_NS}/Robot/mzyd",
        update_period=0.0,
        history_length=6,
        debug_vis=True,
        filter_prim_paths_expr=["{ENV_REGEX_NS}/Cube0"],
    )

    contact_forces_wmzjd = ContactSensorCfg(
        prim_path="{ENV_REGEX_NS}/Robot/wmzjd",
        update_period=0.0,
        history_length=6,
        debug_vis=True,
        filter_prim_paths_expr=["{ENV_REGEX_NS}/Cube0"],
    )

    contact_forces_wmzzd = ContactSensorCfg(
        prim_path="{ENV_REGEX_NS}/Robot/wmzzd",
        update_period=0.0,
        history_length=6,
        debug_vis=True,
        filter_prim_paths_expr=["{ENV_REGEX_NS}/Cube0"],
    )

    contact_forces_wmzyd = ContactSensorCfg(
        prim_path="{ENV_REGEX_NS}/Robot/wmzyd",
        update_period=0.0,
        history_length=6,
        debug_vis=True,
        filter_prim_paths_expr=["{ENV_REGEX_NS}/Cube0"],
    )


def inverse_rotate_quat(quat):
    """
    先进行绕y轴旋转90度（逆变换）
    再进行绕x轴旋转-90度（逆变换）
    四元数是[w,x,y,z]
    """
    quat = R.from_quat(quat)
    quat = quat * R.from_euler("x", 90, degrees=True)  # 逆变换：绕y轴旋转90度
    quat = quat * R.from_euler("y", 90, degrees=True)  # 逆变换：绕x轴旋转90度
    quat = quat.as_quat()

    return quat


def object_end_pos_ik_controller(
    robot,
    robot_entity_cfg,
    time,
    object_pos,
    object_quat,
    fix_pose,
    switch_flag,
    target_pos,
):
    """
    传入物体目标位姿，机械臂移动线性插值过去，移到附近后进行手指使能
    输入:robot,robot_entity_cfg,time,物体坐标系pos,物体坐标系quat,世界坐标原点pos

    return 手的根部位置
    """
    # 直接使用所有关节
    all_joint_ids = robot_entity_cfg.joint_ids

    # 创建初始关节位置的副本
    default_pos = target_pos.clone()
    # real_pos = robot.data.joint_pos.clone()

    # # 计算每个关节的初始位置和目标位置的差值
    # delta_pos = default_pos - real_pos

    # default_pos = real_pos + delta_pos * 0.9

    num_groups = default_pos.shape[0]  # 获取组数

    # 解包位置和姿态
    position_tensor, quaternion_tensor = object_pos, object_quat
    position_tensor_fix = position_tensor - fix_pose

    # print("position_tensor_fix:", position_tensor_fix)

    # 将张量转移到 CPU 上，并转换为 NumPy 数组
    position = position_tensor_fix.cpu().numpy()

    # 为每个环境创建位姿插值
    global_numsmarker = []
    global_numsmarker_plam = []

    # 获取当前手部位置和方向
    root_state = robot.data.root_state_w.clone()
    # print("hand ===> >>>>>>>>>>>>>>>> root_state:", root_state)

    # 使用非常小的固定步长来控制移动
    # 这比使用时间插值更可控
    max_step_size = 0.005  # 每帧最大移动5毫米
    satisfied_count = 0

    for index in range(num_groups):
        # 获取当前位置和目标位置
        current_pos = root_state[index, :3]
        target_pos = (
            torch.tensor(position[index], device=robot.device) + fix_pose[index]
        )

        # 为抓取准备的偏移量
        grip_offset = torch.tensor([-0.045, 0.10, -0.018], device=robot.device)
        # grip_offset = torch.tensor([-0.05, 0.09, -0.018], device=robot.device)

        target_pos = target_pos + grip_offset

        # 计算方向向量和距离
        direction = target_pos - current_pos
        distance = torch.norm(direction)

        # 只有当距离足够大时才移动
        if distance > 0.001:  # 1毫米阈值
            # 正规化方向向量
            direction = direction / distance
            # 限制每帧移动距离
            step_size = min(max_step_size, distance)
            # 计算新位置
            new_pos = current_pos + direction * step_size
        else:
            # 已经非常接近目标，保持当前位置
            new_pos = current_pos
            # switch_flag +=1
            # 增加计数器
            satisfied_count += 1

        # 保持原始旋转，不做任何修改
        current_rot = root_state[index, 3:7]

        # 更新根状态
        root_state[index, :3] = new_pos
        # 保持原始旋转不变
        # root_state[index, 3:7] = current_rot  # 这行实际上是多余的，但为了明确我们保留它

        # 创建手部位置和方向的标记
        hand_pos = new_pos - fix_pose[index]
        hand_rot = current_rot

        # 添加一些偏移以表示手掌位置
        palm_pos = hand_pos + torch.tensor([0.0, 0.0, 0.05], device="cuda:0").float()
        palm_rot = hand_rot

        # 合并位置和方向用于可视化
        global_marker = torch.cat((hand_pos, hand_rot), dim=-1)
        global_numsmarker.append(global_marker)

        global_marker_plam = torch.cat((palm_pos, palm_rot), dim=-1)
        global_numsmarker_plam.append(global_marker_plam)

    # 检查是否所有组都满足条件
    if satisfied_count == num_groups:
        switch_flag += 1

    # if switch_flag >= 4:
    #     switch_flag = 3
    print("root state ===> >>>>>>>>>>>>>>>> root_state:", root_state[:, :7])

    # 写入根状态
    # robot.write_root_state_to_sim(root_state)
    robot.write_root_pose_to_sim(root_state[:, :7])

    global_numsmarker_tensor = torch.stack(global_numsmarker)
    global_numsmarker_plam_tensor = torch.stack(global_numsmarker_plam)

    # 保持关节位置不变，只移动根位置
    return (
        default_pos,
        global_numsmarker_tensor,
        global_numsmarker_plam_tensor,
        switch_flag,
    )


def motion_generator(current_pos, amplitude=0.4, frequency=0.5, flag=None):
    """
    以tlpy的ToraRHandJointName为关节名称，生成关节对象ToraJoints
    """
    if flag == 1:
        joint_data = ToraJoints.right_hand_zeros()  # 顺序为ToraRHandJointName的默认顺序

        # 设置关节位置
        joint_data[ToraRHandJointName.RIGHT_THUMB_CMC_ABDUCTION].position = -0.2
        joint_data[ToraRHandJointName.RIGHT_THUMB_CMC_FLEXION].position = 1.57
        joint_data[ToraRHandJointName.RIGHT_THUMB_MCP_FLEXION].position = 0.00
        joint_data[ToraRHandJointName.RIGHT_THUMB_IP_FLEXION].position = 0.00
        joint_data[ToraRHandJointName.RIGHT_INDEX_MCP_ABDUCTION].position = 0.0
        joint_data[ToraRHandJointName.RIGHT_INDEX_MCP_FLEXION].position = 0.57
        joint_data[ToraRHandJointName.RIGHT_INDEX_PIP_FLEXION].position = 0.57
        joint_data[ToraRHandJointName.RIGHT_MIDDLE_MCP_ABDUCTION].position = 0.0
        joint_data[ToraRHandJointName.RIGHT_MIDDLE_MCP_FLEXION].position = 0.57
        joint_data[ToraRHandJointName.RIGHT_MIDDLE_PIP_FLEXION].position = 0.57
        joint_data[ToraRHandJointName.RIGHT_RING_MCP_ABDUCTION].position = 0.0
        joint_data[ToraRHandJointName.RIGHT_RING_MCP_FLEXION].position = 0.57
        joint_data[ToraRHandJointName.RIGHT_RING_PIP_FLEXION].position = 0.57
        # 或
    # joint_data.set_positions([0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0])
    elif flag == 2:
        joint_data = ToraJoints.right_hand_zeros()  # 顺序为ToraRHandJointName的默认顺序

        # 设置关节位置
        joint_data[ToraRHandJointName.RIGHT_THUMB_CMC_ABDUCTION].position = -0.2
        joint_data[ToraRHandJointName.RIGHT_THUMB_CMC_FLEXION].position = 1.57
        joint_data[ToraRHandJointName.RIGHT_THUMB_MCP_FLEXION].position = 1.30
        joint_data[ToraRHandJointName.RIGHT_THUMB_IP_FLEXION].position = 1.57
        joint_data[ToraRHandJointName.RIGHT_INDEX_MCP_ABDUCTION].position = 0.0
        joint_data[ToraRHandJointName.RIGHT_INDEX_MCP_FLEXION].position = 2.07
        joint_data[ToraRHandJointName.RIGHT_INDEX_PIP_FLEXION].position = 1.57
        joint_data[ToraRHandJointName.RIGHT_MIDDLE_MCP_ABDUCTION].position = 0.0
        joint_data[ToraRHandJointName.RIGHT_MIDDLE_MCP_FLEXION].position = 2.07
        joint_data[ToraRHandJointName.RIGHT_MIDDLE_PIP_FLEXION].position = 1.57
        joint_data[ToraRHandJointName.RIGHT_RING_MCP_ABDUCTION].position = 0.0
        joint_data[ToraRHandJointName.RIGHT_RING_MCP_FLEXION].position = 2.07
        joint_data[ToraRHandJointName.RIGHT_RING_PIP_FLEXION].position = 1.57
    else:
        joint_data = ToraJoints.right_hand_zeros()  # 顺序为ToraRHandJointName的默认顺序
    return joint_data


def rgrasp_object_1(
    robot,
    object,
    object_target_w,
    sim_hand_driver,
    switch_flag,
    contact_mzzd,
    contact_szyd,
    contact_zzyd,
    object_time_sz,
    object_time_zz,
):
    """
    控制灵巧手到指定的关节角度

    参数说明:
    - robot: 机器人对象
    - robot_entity_cfg: 机器人配置
    - target_joints: 目标关节角度列表，顺序如下:
      [0] mzcb: 拇指掌长本节 - 拇指基部弯曲
      [1] mzjdxz: 拇指近端旋转 - 拇指近端内外侧屈
      [2] mzzd: 拇指中段 - 拇指第一指节弯曲
      [3] mzydxz: 拇指远端旋转 - 拇指末端内外侧屈

      [4] szcb: 食指掌长本节 - 食指基部弯曲
      [5] szjdxz: 食指近端旋转 - 食指近端内外侧屈
      [6] szzdxz: 食指中段旋转 - 食指中段内外侧屈
      [7] szydxz: 食指远端旋转 - 食指末端内外侧屈

      [8] zzcb: 中指掌长本节 - 中指基部弯曲
      [9] zzjdxz: 中指近端旋转 - 中指近端内外侧屈
      [10] zzzdxz: 中指中段旋转 - 中指中段内外侧屈
      [11] zzydxz: 中指远端旋转 - 中指末端内外侧屈

      [12] wmzcb: 无名指掌长本节 - 无名指基部弯曲
      [13] wmzjdxz: 无名指近端旋转 - 无名指近端内外侧屈
      [14] wmzzdxz: 无名指中段旋转 - 无名指中段内外侧屈
      [15] wmzydxz: 无名指远端旋转 - 无名指末端内外侧屈

    注: 每个手指有4个关节，从掌长本关节到远端关节。
        正值通常表示弯曲/内侧屈，负值表示伸展/外侧屈。
    """
    sharklog.debug(f"joint_names: {robot.data.joint_names}")
    default_pos = robot.data.joint_pos.clone().squeeze()  # 移除多余的维度
    # 初始化上一次的关节位置（存储完整的关节数据）
    if not hasattr(robot, "previous_joints"):
        robot.previous_joints = default_pos.clone()
    current_pos = default_pos.clone()
    joint_data = motion_generator(current_pos, flag=switch_flag)
    right_hand_joint_position_target = sim_hand_driver.tora_joints2sim_joints(
        joint_data
    )

    right_hand_joint_position_target = torch.tensor(
        right_hand_joint_position_target,
        dtype=robot.data.joint_pos.dtype,
        device=robot.data.joint_pos.device,
    )
    object_real_w = object.data.root_state_w.clone()
    if switch_flag < 3:
        joint_flag = torch.allclose(
            right_hand_joint_position_target, default_pos, atol=1e-3
        )
    if joint_flag:
        print("已达到目标位置死区")

    object_real_pose = object_real_w[:, :7]
    object_target_pose = object_target_w[:, :7]
    # print("object_real_pose:", object_real_pose)
    # print("object_target_pose:", object_target_pose)
    object_flag = torch.any(torch.abs(object_real_pose - object_target_pose) > (5e-1))
    if object_flag:
        print("物体状态变化已超死区")

    contact_mzzd_data = contact_mzzd.data.net_forces_w
    contact_szyd_data = contact_szyd.data.net_forces_w
    contact_zzyd_data = contact_zzyd.data.net_forces_w

    # 检查是否有任何接触
    object_flag_mz = False
    object_flag_sz = False
    object_flag_zz = False

    print("contact_mzyd_data:", torch.abs(contact_mzzd_data[:, :, :]))
    print("contact_szyd_data:", torch.abs(contact_szyd_data[:, :, :]))
    print("contact_zzyd_data:", torch.abs(contact_zzyd_data[:, :, :]))

    print("contact_mzyd_data_shape:", contact_mzzd_data.shape)
    if (
        torch.abs(contact_mzzd_data[0, :, 0]) >= 0
        and torch.abs(contact_mzzd_data[0, :, 1]) >= 0
        and torch.abs(contact_mzzd_data[0, :, 2]) >= 0
    ):
        print("拇指接触到物体")
        object_flag_mz = True

    # if torch.abs(contact_szyd_data[0,:,0]) >=40 and torch.abs(contact_szyd_data[0,:,1]) >=100 and torch.abs(contact_szyd_data[0,:,2]) >=5:
    #     print("食指接触到物体")
    #     object_time_sz += 1

    # if torch.abs(contact_zzyd_data[0,:,0]) >=50 and torch.abs(contact_zzyd_data[0,:,1]) >=100 and torch.abs(contact_zzyd_data[0,:,2]) >=5:
    #     print("中指接触到物体")
    #     object_time_zz += 1

    if (
        torch.abs(contact_szyd_data[0, :, 0]) >= 20
        and torch.abs(contact_szyd_data[0, :, 1]) >= 100
        and torch.abs(contact_szyd_data[0, :, 2]) >= 5
    ):
        print("食指接触到物体")
        object_time_sz += 1

    if (
        torch.abs(contact_zzyd_data[0, :, 0]) >= 20
        and torch.abs(contact_zzyd_data[0, :, 1]) >= 100
        and torch.abs(contact_zzyd_data[0, :, 2]) >= 5
    ):
        print("中指接触到物体")
        object_time_zz += 1

    if object_time_sz >= 10:
        object_flag_sz = True

    if object_time_zz >= 10:
        object_flag_zz = True

    if object_flag_mz and object_flag_zz and object_flag_sz:
        print("接触到物体")
        object_flag = True

    if joint_flag or object_flag:
        switch_flag += 1
        joint_flag = False
        object_flag = False
    return right_hand_joint_position_target, switch_flag, object_time_sz, object_time_zz


def lgrasp_object_1(robot, robot_entity_cfg, target_joints):
    """
    使能左手抓取手势->输入灵巧手左关节
    -parms:
        -robot
        -robot_entity_cfg
        -target_joints
    return:
        -whole_body_joint
    """
    specific_indices_LDexH13 = [
        14,
        22,
        30,
        38,
        15,
        23,
        31,
        39,
        16,
        24,
        32,
        40,
        17,
        25,
        33,
        41,
    ]

    selected_joint_LDexH13_ids = [
        robot_entity_cfg.joint_ids[i] for i in specific_indices_LDexH13
    ]
    print("selected_joint_DexH13_ids:", selected_joint_LDexH13_ids)

    joint_data = robot.data.joint_pos.clone()
    target_joints = torch.tensor(
        target_joints, dtype=joint_data.dtype, device=joint_data.device
    )

    joint_data[:, selected_joint_LDexH13_ids] = target_joints

    return joint_data


def run_simulator(sim: sim_utils.SimulationContext, scene: InteractiveScene):
    """Run the simulator."""
    sim_dt = sim.get_physics_dt()
    sim_time = 0.0
    count = 0
    robot = scene["right_hand"]
    bottle = scene["bottle"]

    # 触觉传感器
    contact_szjd = scene["contact_forces_szjd"]
    contact_szzd = scene["contact_forces_szzd"]
    contact_szyd = scene["contact_forces_szyd"]

    contact_mzzd = scene["contact_forces_mzzd"]
    contact_mzyd = scene["contact_forces_mzyd"]

    contact_zzjd = scene["contact_forces_zzjd"]
    contact_zzzd = scene["contact_forces_zzzd"]
    contact_zzyd = scene["contact_forces_zzyd"]

    contact_wmzjd = scene["contact_forces_wmzjd"]
    contact_wmzzd = scene["contact_forces_wmzzd"]
    contact_wmzyd = scene["contact_forces_wmzyd"]

    sharklog.debug(f"joint_names: {robot.data.joint_names}")
    sim_hand_driver = IsaacBadRHandDriver(
        robot.data.joint_names, ToraRHandJointName.items()
    )
    sharklog.debug("sim_hand_driver: loaded")

    grasp_start_time = None  # 初始化为None
    last_switch_flag = 0  # 记录上一帧的标志值
    robot_entity_cfg = SceneEntityCfg(
        "right_hand",
        joint_names=[
            "mzcb",
            "mzjdxz",
            "mzzd",
            "mzydxz",
            "szcb",
            "szjdxz",
            "szzdxz",
            "szydxz",
            "zzcb",
            "zzjdxz",
            "zzzdxz",
            "zzydxz",
            "wmzcb",
            "wmzjdxz",
            "wmzzdxz",
            "wmzydxz",
        ],
        body_names=[],
    )
    robot_entity_cfg.resolve(scene)
    print("robot_entity_cfg:", robot_entity_cfg)

    frame_marker_cfg = FRAME_MARKER_CFG.copy()
    frame_marker_cfg.markers["frame"].scale = (0.1, 0.1, 0.1)

    ee_marker = VisualizationMarkers(
        frame_marker_cfg.replace(prim_path="/Visuals/ee_current")
    )
    goal_marker = VisualizationMarkers(
        frame_marker_cfg.replace(prim_path="/Visuals/ee_goal")
    )

    goal_marker_plam = VisualizationMarkers(
        frame_marker_cfg.replace(prim_path="/Visuals/ee_goal")
    )

    ee_lmarker = VisualizationMarkers(
        frame_marker_cfg.replace(prim_path="/Visuals/ee_current")
    )

    goal_lmarker = VisualizationMarkers(
        frame_marker_cfg.replace(prim_path="/Visuals/ee_goal")
    )

    target = bottle.data.default_root_state.clone()

    position_target = target[:, :3] + scene.env_origins  # 包含原点

    quaternion_target = target[:, 3:7]

    robot_state = robot.data.default_root_state.clone()

    robot_state[:, :3] += scene.env_origins

    robot.write_root_state_to_sim(robot_state)
    # joint state

    joint_pos, joint_vel = (
        robot.data.default_joint_pos.clone(),
        robot.data.default_joint_vel.clone(),
    )

    robot.write_joint_state_to_sim(joint_pos, joint_vel)
    # reset the internal state

    robot.reset()

    fix_pose = scene.env_origins
    # print("env_origins:",fix_pose)
    # 建立状态机

    switch_flag = 0

    # position_target_tensor = bottle.data.body_pos_w.clone()
    atol = 1.5e-2
    # 新增一个计数器
    stay_in_state_3_counter = 0
    stay_in_state_3_threshold = 100  # 例如，保持 10 个步骤

    object_target_w = bottle.data.root_state_w.clone()
    object_time_sz, object_time_zz = 0, 0
    while simulation_app.is_running():
        # print("Index Received contact forces:", contact_szyd.data.net_forces_w)

        # print("Thumb Received contact forces:", contact_mzyd.data.net_forces_w)
        joint_real_pos = robot.data.joint_pos.clone()
        match switch_flag:
            case 0:
                targets, target_markers, target_markers_plam, switch_flag = (
                    object_end_pos_ik_controller(
                        robot,
                        robot_entity_cfg,
                        sim_time,
                        position_target,
                        quaternion_target,
                        fix_pose,
                        switch_flag,
                        joint_real_pos,
                    )
                )
            case 1:
                targets, switch_flag, object_time_sz, object_time_zz = rgrasp_object_1(
                    robot,
                    bottle,
                    object_target_w,
                    sim_hand_driver,
                    switch_flag,
                    contact_mzzd,
                    contact_szyd,
                    contact_zzyd,
                    object_time_sz,
                    object_time_zz,
                )
            case 2:
                targets, switch_flag, object_time_sz, object_time_zz = rgrasp_object_1(
                    robot,
                    bottle,
                    object_target_w,
                    sim_hand_driver,
                    switch_flag,
                    contact_mzzd,
                    contact_szyd,
                    contact_zzyd,
                    object_time_sz,
                    object_time_zz,
                )
            case 3:
                position_target_clone = position_target.clone()
                position_target_clone[:, 2] += 0.1
                targets, target_markers, target_markers_plam, switch_flag = (
                    object_end_pos_ik_controller(
                        robot,
                        robot_entity_cfg,
                        sim_time,
                        position_target_clone,
                        quaternion_target,
                        fix_pose,
                        switch_flag,
                        targets,
                    )
                )
        print("switch_flag:", switch_flag)

        robot.set_joint_position_target(targets)

        scene.write_data_to_sim()
        # Step simulation
        sim.step()
        # Update time
        sim_time += sim_dt
        count += 1
        # Update scene
        scene.update(sim_dt)


def main():
    """Main function."""
    # Initialize simulation
    # sim_cfg = sim_utils.SimulationCfg(dt=0.004, device=args_cli.device)
    sim_cfg = sim_utils.SimulationCfg(
        dt=0.01,
        render_interval=2,
        # disable_contact_processing=True,
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="multiply",
            restitution_combine_mode="multiply",
            static_friction=2.5,
            dynamic_friction=2.5,
            restitution=0.1,
        ),
    )
    sim = sim_utils.SimulationContext(sim_cfg)

    # Set camera view
    sim.set_camera_view(eye=[3.0, -3.0, 2.25], target=[0.0, 0.0, 0.0])

    # Create scene
    scene_cfg = TableSceneCfg(num_envs=args_cli.num_envs, env_spacing=2.0)
    scene = InteractiveScene(scene_cfg)

    # Reset simulation
    sim.reset()

    print("[INFO]: Setup complete...")
    # Run simulator
    run_simulator(sim, scene)


if __name__ == "__main__":
    main()
    simulation_app.close()
