"""DexH13_right.py

Configuration for the dexterous hand from PAXINI Tech

@Author: Liangyi
@Date: 2024-11-11
"""

from __future__ import annotations

import isaaclab.sim as sim_utils
from isaaclab.actuators import ImplicitActuatorCfg
from isaaclab.assets import ArticulationCfg
from px_janus_learnsim.config.paths import ROBOT_MODELS_PATH

"""This hand's urdf and usd file, the naming of the joints are different from the tora_one whole body model"""

# Configuration
##
"""DexH13 isaacSim配置"""
DexH13_right_CFG = ArticulationCfg(
    spawn=sim_utils.UsdFileCfg(
        usd_path=ROBOT_MODELS_PATH["DEXH13_RIGHT"]["USD"],
        activate_contact_sensors=False,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=True,
            retain_accelerations=False,
            max_depenetration_velocity=1000.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=64,
            solver_velocity_iteration_count=64,
            sleep_threshold=0.005,
            stabilization_threshold=0.0005,
            fix_root_link=True,
        ),
        joint_drive_props=sim_utils.JointDrivePropertiesCfg(drive_type="position"),
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0.0, 0.0, 1),  # 调整初始位置
        rot=(0.0, 0.0, -0.7071, 0.7071),
        lin_vel=(0.0, 0.0, 0.0),
        ang_vel=(0.0, 0.0, 0.0),
        joint_pos={
            # 拇指关节
            "mzcb": 0.00,  # 拇指侧摆
            "mzjdxz": 0.00,  # 拇指近端旋转
            "mzzd": 0.00,  # 拇指远端旋转
            "mzydxz": 0.00,  # 拇指掌端旋转
            # 食指关节
            "szcb": 0.0,  # 食指侧摆
            "szjdxz": 0.0,  # 食指近端旋转
            "szzdxz": 0.0,  # 食指远端旋转
            "szydxz": 0.0,  # 食指掌端旋转
            # 中指关节
            "zzcb": 0.0,  # 中指侧摆
            "zzjdxz": 0.0,  # 中指近端旋转
            "zzzdxz": 0.0,  # 中指远端旋转
            "zzydxz": 0.0,  # 中指掌端旋转
            # 无名指关节
            "wmzcb": 0.0,  # 无名指侧摆
            "wmzjdxz": 0.0,  # 无名指近端旋转
            "wmzzdxz": 0.0,  # 无名指远端旋转
            "wmzydxz": 0.0,  # 无名指掌端旋转
        },
        joint_vel={".*": 0.0},  # 所有关节初始速度为0
    ),
    soft_joint_pos_limit_factor=0.9,
    actuators={
        "hands": ImplicitActuatorCfg(
            joint_names_expr=[
                "mzcb",
                "mzjdxz",
                "mzzd",
                "mzydxz",
                "szcb",
                "szjdxz",
                "szzdxz",
                "szydxz",
                "zzcb",
                "zzjdxz",
                "zzzdxz",
                "zzydxz",
                "wmzcb",
                "wmzjdxz",
                "wmzzdxz",
                "wmzydxz",
            ],
            effort_limit=100,
            velocity_limit=50.0,
            stiffness={
                "mzcb": 2000.0,
                "mzjdxz": 2000.0,
                "mzzd": 2000.0,
                "mzydxz": 2000.0,
                "szcb": 2000.0,
                "szjdxz": 2000.0,
                "szzdxz": 2000.0,
                "szydxz": 2000.0,
                "zzcb": 2000.0,
                "zzjdxz": 2000.0,
                "zzzdxz": 2000.0,
                "zzydxz": 2000.0,
                "wmzcb": 2000.0,
                "wmzjdxz": 2000.0,
                "wmzzdxz": 2000.0,
                "wmzydxz": 2000.0,
            },
            damping={
                "mzcb": 100.0,
                "mzjdxz": 100.0,
                "mzzd": 100.0,
                "mzydxz": 100.0,
                "szcb": 100.0,
                "szjdxz": 100.0,
                "szzdxz": 100.0,
                "szydxz": 100.0,
                "zzcb": 100.0,
                "zzjdxz": 100.0,
                "zzzdxz": 100.0,
                "zzydxz": 100.0,
                "wmzcb": 100.0,
                "wmzjdxz": 100.0,
                "wmzzdxz": 100.0,
                "wmzydxz": 100.0,
            },
            # armature={
            #     "mzcb": 0.001,
            #     "mzjdxz": 0.001,
            #     "mzzd": 0.001,
            #     "mzydxz": 0.001,
            #     "szcb": 0.001,
            #     "szjdxz": 0.001,
            #     "szzdxz": 0.001,
            #     "szydxz": 0.001,
            #     "zzcb": 0.001,
            #     "zzjdxz": 0.001,
            #     "zzzdxz": 0.001,
            #     "zzydxz": 0.001,
            #     "wmzcb": 0.001,
            #     "wmzjdxz": 0.001,
            #     "wmzzdxz": 0.001,
            #     "wmzydxz": 0.001,
            # },
        ),
    },
)
