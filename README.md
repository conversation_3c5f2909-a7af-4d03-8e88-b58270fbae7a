# InspireHandForceControlRL

本项目旨在使用 Soft Actor-Critic (SAC) 强化学习算法，在 Isaac Sim 仿真环境中训练一个模型，以控制 Inspire Hand 右手模型抓取一个球体，并在每个指尖上实现预定的接触力目标。

## 依赖项

*   **Python:** 推荐使用 Python 3.10 (请根据您的 Isaac Sim 版本确认兼容性)。
*   **Isaac Sim:** 需要 Isaac Sim 2023.x.x 或更高版本 (请根据您的实际版本进行验证)。确保已安装 Isaac Lab。
*   **Python 包:** 核心依赖项列在 `requirements.txt` 文件中。`torch` 和 `numpy` 通常随 Isaac Sim 一起提供。

## 安装与设置

1.  **打开仓库:**
    ```bash

    cd rl_sac_ForceControl
    ```
2.  **设置 Isaac Sim 环境:**
    *   确保您的 Isaac Sim 环境已正确安装并配置好 Isaac Lab。请参考官方 Isaac Lab 文档进行设置。
    *   建议在 Isaac Sim 的 Python 环境中操作，或者使用其提供的 `python.sh` 脚本。
3.  **安装 Python 依赖:**
    ```bash
    # 确保您使用的是 Isaac Sim 对应的 Python 环境
    pip install -r requirements.txt
    ```

## 基本用法 (评估/可视化)

您可以使用训练脚本加载已保存的模型，并进行可视化以评估其性能：

### 评估标准模型

```bash
# 假设您的 Isaac Sim Python 环境已激活
# 或者使用 ./python.sh scripts/train_inspire_sac.py ...

python scripts/train_inspire_sac.py --task Isaac-InspireHandForce-Direct-v0 --load_model logs/sb3/Isaac-InspireHandForce-Direct-v0/<your_run_folder>/model_final.zip --video --num_envs 1
```

### 评估自定义网络模型

如果您的模型是使用自定义网络训练的，评估时也需要指定相同的网络配置：

```bash
# 评估使用自定义网络训练的模型
python scripts/train_inspire_sac.py --task Isaac-InspireHandForce-Direct-v0 --load_model logs/sb3/Isaac-InspireHandForce-Direct-v0/<your_run_folder>/model_final.zip --custom_policy --network_config <same_config_as_training> --video --num_envs 1
```

### 参数说明

*   将 `<your_run_folder>` 替换为您训练运行生成的日志目录名称。
*   `--video` 参数会启用渲染和视频录制（保存在日志目录的 `videos` 子文件夹中）。
*   `--num_envs 1` 通常用于可视化，只运行一个环境实例。
*   使用自定义网络训练的模型，评估时必须指定相同的 `--custom_policy` 和 `--network_config` 参数。

## 训练

### 基本训练

要开始新的训练运行，请执行以下命令：

```bash
# 假设您的 Isaac Sim Python 环境已激活
# 或者使用 ./python.sh scripts/train_inspire_sac.py ...

python scripts/train_inspire_sac.py --task Isaac-InspireHandForce-Direct-v0 --max_iterations 5000000
```

### 使用自定义神经网络结构

本项目支持使用自定义神经网络结构来替代默认的MLP策略。您可以通过以下方式使用：

#### 1. 使用预设网络配置

```bash
# 使用基本MLP配置（默认）
python scripts/train_inspire_sac.py --task Isaac-InspireHandForce-Direct-v0 --custom_policy --network_config basic_mlp --max_iterations 5000000

# 使用三层256维MLP配置
python scripts/train_inspire_sac.py --task Isaac-InspireHandForce-Direct-v0 --custom_policy --network_config triple_256_mlp --max_iterations 5000000

# 使用注意力网络配置
python scripts/train_inspire_sac.py --task Isaac-InspireHandForce-Direct-v0 --custom_policy --network_config attention_net --max_iterations 5000000

# 使用混合网络配置
python scripts/train_inspire_sac.py --task Isaac-InspireHandForce-Direct-v0 --custom_policy --network_config hybrid_net --max_iterations 5000000
```

#### 2. 使用自定义配置文件

您也可以创建自己的网络配置文件并使用：

```bash
# 使用自定义YAML配置文件
python scripts/train_inspire_sac.py --task Isaac-InspireHandForce-Direct-v0 --custom_policy --network_config path/to/your/config.yaml --max_iterations 5000000
```

#### 可用的预设网络配置

*   **basic_mlp**: 基础多层感知机，适合快速实验
*   **triple_256_mlp**: 三层256维MLP，平衡性能与计算效率
*   **attention_net**: 基于注意力机制的网络，适合复杂观测空间
*   **hybrid_net**: 混合网络结构，结合多种网络组件

#### 自定义网络配置参数

*   `--custom_policy`: 启用自定义SAC策略（必需参数）
*   `--network_config`: 指定网络配置预设名称或配置文件路径

### 训练说明

*   训练日志、模型检查点和 TensorBoard 文件将保存在 `logs/sb3/Isaac-InspireHandForce-Direct-v0/` 下的时间戳子目录中。
*   使用自定义网络时，训练开始时会显示网络架构信息，包括参数数量和模型大小。
*   有关训练过程、环境细节和超参数的更详细信息，请参阅 [训练指南](./docs/training_guide.md)。

## 项目结构

```
InspireHandForceControlRL/
├── config/             # 包含机器人和对象的配置文件
│   ├── inspire_hand_cfg.py
│   └── objects_cfg.py
├── envs/               # 包含自定义 RL 环境
│   └── inspire_hand_force_env.py
├── scripts/            # 包含训练和评估脚本
│   └── train_inspire_sac.py
├── logs/               # 存储训练日志、模型和 TensorBoard 文件 (自动生成)
├── docs/               # 包含详细文档
│   └── training_guide.md
├── requirements.txt    # Python 依赖项列表
└── README.md           # 本文件
``` 